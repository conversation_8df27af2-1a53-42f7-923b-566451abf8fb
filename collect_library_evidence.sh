#!/bin/bash

# Library Symbol Export Issue Evidence Collection
# This script collects comprehensive evidence of symbol export problems in vendor libraries

REPORT_FILE="library_symbol_export_issues_$(date +%Y%m%d_%H%M%S).txt"

echo "========================================" > $REPORT_FILE
echo "LIBRARY SYMBOL EXPORT ISSUES REPORT" >> $REPORT_FILE
echo "Generated on: $(date)" >> $REPORT_FILE
echo "System: $(uname -a)" >> $REPORT_FILE
echo "Architecture: $(uname -m)" >> $REPORT_FILE
echo "========================================" >> $REPORT_FILE
echo "" >> $REPORT_FILE

echo "SUMMARY OF ISSUES:" >> $REPORT_FILE
echo "All vendor-provided ARM64 libraries have symbol export problems:" >> $REPORT_FILE
echo "- Functions exist in the binary (visible in strings)" >> $REPORT_FILE
echo "- Functions are NOT exported as dynamic symbols" >> $REPORT_FILE
echo "- dlsym() cannot find the functions at runtime" >> $REPORT_FILE
echo "- This makes the libraries unusable for dynamic loading" >> $REPORT_FILE
echo "" >> $REPORT_FILE

# List of libraries to check
LIBRARIES=(
    "lib/libG2_encryption.so"
    "lib/libG1_alg1.so"
    "lib/libG1_alg3.so"
    "lib/libC303.so"
    "lib/libencryptalg1.so"
    "lib/libencryptalg2.so"
)

# Expected functions for each library
declare -A EXPECTED_FUNCTIONS
EXPECTED_FUNCTIONS["lib/libG2_encryption.so"]="G2_VinToSc G2_CalcInCode"
EXPECTED_FUNCTIONS["lib/libG1_alg1.so"]="Trans_InputCode"
EXPECTED_FUNCTIONS["lib/libG1_alg3.so"]="CalcInCode"
EXPECTED_FUNCTIONS["lib/libC303.so"]="alg"
EXPECTED_FUNCTIONS["lib/libencryptalg1.so"]="EncryptAlg1"
EXPECTED_FUNCTIONS["lib/libencryptalg2.so"]="EncryptAlg2"

echo "DETAILED ANALYSIS:" >> $REPORT_FILE
echo "" >> $REPORT_FILE

for lib in "${LIBRARIES[@]}"; do
    echo "========================================" >> $REPORT_FILE
    echo "LIBRARY: $lib" >> $REPORT_FILE
    echo "========================================" >> $REPORT_FILE
    
    if [ -f "$lib" ]; then
        echo "✅ Library file exists" >> $REPORT_FILE
        
        # Check file type and architecture
        echo "" >> $REPORT_FILE
        echo "FILE INFORMATION:" >> $REPORT_FILE
        file "$lib" >> $REPORT_FILE
        
        # Check file size
        echo "" >> $REPORT_FILE
        echo "FILE SIZE:" >> $REPORT_FILE
        ls -lh "$lib" >> $REPORT_FILE
        
        # Check dynamic symbol table (exported symbols)
        echo "" >> $REPORT_FILE
        echo "EXPORTED DYNAMIC SYMBOLS (nm -D):" >> $REPORT_FILE
        nm -D "$lib" 2>/dev/null >> $REPORT_FILE
        if [ $? -ne 0 ]; then
            echo "❌ Failed to read dynamic symbols" >> $REPORT_FILE
        fi
        
        # Check dynamic symbol table with objdump
        echo "" >> $REPORT_FILE
        echo "DYNAMIC SYMBOL TABLE (objdump -T):" >> $REPORT_FILE
        objdump -T "$lib" 2>/dev/null >> $REPORT_FILE
        if [ $? -ne 0 ]; then
            echo "❌ Failed to read dynamic symbol table" >> $REPORT_FILE
        fi
        
        # Check all symbols (including non-exported)
        echo "" >> $REPORT_FILE
        echo "ALL SYMBOLS (nm):" >> $REPORT_FILE
        nm "$lib" 2>/dev/null | head -20 >> $REPORT_FILE
        echo "... (truncated, showing first 20 symbols)" >> $REPORT_FILE
        
        # Check strings for expected function names
        echo "" >> $REPORT_FILE
        echo "FUNCTION NAMES IN STRINGS:" >> $REPORT_FILE
        expected_funcs=${EXPECTED_FUNCTIONS[$lib]}
        for func in $expected_funcs; do
            echo "Searching for: $func" >> $REPORT_FILE
            if strings "$lib" | grep -q "$func"; then
                echo "  ✅ FOUND in strings: $func" >> $REPORT_FILE
                strings "$lib" | grep "$func" >> $REPORT_FILE
            else
                echo "  ❌ NOT FOUND in strings: $func" >> $REPORT_FILE
            fi
        done
        
        # Check if functions are in symbol table but not exported
        echo "" >> $REPORT_FILE
        echo "SYMBOL TABLE ANALYSIS:" >> $REPORT_FILE
        for func in $expected_funcs; do
            echo "Checking symbol table for: $func" >> $REPORT_FILE
            if nm "$lib" 2>/dev/null | grep -q "$func"; then
                echo "  ✅ FOUND in symbol table: $func" >> $REPORT_FILE
                nm "$lib" 2>/dev/null | grep "$func" >> $REPORT_FILE
                
                # Check if it's exported
                if nm -D "$lib" 2>/dev/null | grep -q "$func"; then
                    echo "  ✅ EXPORTED as dynamic symbol" >> $REPORT_FILE
                else
                    echo "  ❌ NOT EXPORTED as dynamic symbol" >> $REPORT_FILE
                    echo "     This is the PROBLEM: Function exists but is not accessible via dlsym()" >> $REPORT_FILE
                fi
            else
                echo "  ❌ NOT FOUND in symbol table: $func" >> $REPORT_FILE
            fi
        done
        
        # Runtime test
        echo "" >> $REPORT_FILE
        echo "RUNTIME LOADING TEST:" >> $REPORT_FILE
        
        # Create a simple test program
        cat > test_lib_loading.c << EOF
#include <stdio.h>
#include <dlfcn.h>

int main() {
    void* handle = dlopen("$lib", RTLD_LAZY);
    if (!handle) {
        printf("❌ Failed to load library: %s\n", dlerror());
        return 1;
    }
    printf("✅ Library loaded successfully\n");
    
EOF
        
        for func in $expected_funcs; do
            cat >> test_lib_loading.c << EOF
    void* ${func}_ptr = dlsym(handle, "$func");
    if (${func}_ptr) {
        printf("✅ Function $func found via dlsym\n");
    } else {
        printf("❌ Function $func NOT found via dlsym: %s\n", dlerror());
    }
    
EOF
        done
        
        cat >> test_lib_loading.c << EOF
    dlclose(handle);
    return 0;
}
EOF
        
        # Compile and run the test
        if gcc -o test_lib_loading test_lib_loading.c -ldl 2>/dev/null; then
            echo "Runtime loading test results:" >> $REPORT_FILE
            ./test_lib_loading >> $REPORT_FILE 2>&1
            rm -f test_lib_loading test_lib_loading.c
        else
            echo "❌ Failed to compile runtime test" >> $REPORT_FILE
            rm -f test_lib_loading.c
        fi
        
    else
        echo "❌ Library file does not exist" >> $REPORT_FILE
    fi
    
    echo "" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
done

# Summary of issues
echo "========================================" >> $REPORT_FILE
echo "SUMMARY OF FINDINGS" >> $REPORT_FILE
echo "========================================" >> $REPORT_FILE
echo "" >> $REPORT_FILE

echo "CRITICAL ISSUES FOUND:" >> $REPORT_FILE
echo "" >> $REPORT_FILE

for lib in "${LIBRARIES[@]}"; do
    if [ -f "$lib" ]; then
        echo "Library: $lib" >> $REPORT_FILE
        expected_funcs=${EXPECTED_FUNCTIONS[$lib]}
        
        for func in $expected_funcs; do
            # Check if function exists in strings but not in dynamic symbols
            if strings "$lib" | grep -q "$func"; then
                if ! nm -D "$lib" 2>/dev/null | grep -q "$func"; then
                    echo "  ❌ CRITICAL: $func exists in binary but NOT exported" >> $REPORT_FILE
                fi
            fi
        done
        echo "" >> $REPORT_FILE
    fi
done

echo "IMPACT:" >> $REPORT_FILE
echo "- All libraries are unusable for dynamic loading" >> $REPORT_FILE
echo "- dlopen() can load the libraries" >> $REPORT_FILE
echo "- dlsym() cannot find any of the expected functions" >> $REPORT_FILE
echo "- This prevents the Algorithm.cpp from working correctly" >> $REPORT_FILE
echo "- All algorithm functions return zero/empty results" >> $REPORT_FILE
echo "" >> $REPORT_FILE

echo "REQUIRED FIX:" >> $REPORT_FILE
echo "The vendor needs to recompile all libraries with proper symbol export:" >> $REPORT_FILE
echo "- Use -fvisibility=default for public functions" >> $REPORT_FILE
echo "- Ensure functions are declared with proper linkage" >> $REPORT_FILE
echo "- Verify exported symbols with 'nm -D' before delivery" >> $REPORT_FILE
echo "- Test dynamic loading with dlsym() before delivery" >> $REPORT_FILE
echo "" >> $REPORT_FILE

echo "VERIFICATION COMMANDS:" >> $REPORT_FILE
echo "To verify the fix, vendor should run these commands:" >> $REPORT_FILE
echo "  nm -D <library.so>  # Should show the expected functions" >> $REPORT_FILE
echo "  objdump -T <library.so>  # Should show functions in DYNAMIC SYMBOL TABLE" >> $REPORT_FILE
echo "" >> $REPORT_FILE

echo "========================================" >> $REPORT_FILE
echo "END OF REPORT" >> $REPORT_FILE
echo "========================================" >> $REPORT_FILE

echo "Evidence collection completed!"
echo "Report saved to: $REPORT_FILE"
echo ""
echo "You can send this report to the vendor as proof of the symbol export issues."
