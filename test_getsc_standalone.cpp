// Standalone GetSc test without JNI dependencies
// This extracts just the GetSc logic from Algorithm.cpp

#include <iostream>
#include <iomanip>
#include <cstring>
#include <dlfcn.h>

void printHex(const char* label, const unsigned char* data, int len) {
    std::cout << std::setw(12) << std::left << label << ": ";
    for (int i = 0; i < len; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)data[i];
    }
    std::cout << std::dec << std::endl;
}

// Linux equivalents for Windows API
#define HMODULE void*
#define LoadLibrary(name) dlopen(name, RTLD_LAZY)
#define GetProcAddress(handle, name) dlsym(handle, name)
#define FreeLibrary(handle) dlclose(handle)

// Standalone GetSc function (extracted from Algorithm.cpp)
unsigned char* GetSc_Standalone(unsigned char* vinCode) {
    unsigned char v[6] = {0};
    for (int i = 12; i < 17; i++) // Get last 5 chars of VIN
    {
        v[i - 12] = vinCode[i];
    }
    
    unsigned char* outputcode = new unsigned char[10];
    memset(outputcode, 0, sizeof(unsigned char) * 10);
    
    typedef void (*alg1)(unsigned char*, unsigned char*);
    HMODULE hmodule = LoadLibrary("./lib/libG1_alg1.so");
    
    if (NULL == hmodule) {
        std::cout << "? Failed to load libG1_alg1.so: " << dlerror() << std::endl;
        return outputcode;
    }
    
    std::cout << "? Successfully loaded libG1_alg1.so" << std::endl;
    
    alg1 fun = (alg1)GetProcAddress(hmodule, "Trans_InputCode");
    if (NULL != fun) {
        std::cout << "? Found Trans_InputCode function" << std::endl;
        fun(v, outputcode);
        std::cout << "? Called Trans_InputCode successfully" << std::endl;
    } else {
        std::cout << "? Trans_InputCode function not found: " << dlerror() << std::endl;
    }
    
    FreeLibrary(hmodule);
    return outputcode;
}

int main() {
    std::cout << "=== Standalone GetSc Test ===" << std::endl;
    std::cout << "Testing GetSc logic without JNI dependencies" << std::endl;
    
    // Test Case 1: Windows tool case
    std::cout << "\n--- Test Case 1: Windows Tool VIN ---" << std::endl;
    
    std::string vin_str = "LFPHC7CC6L1B75928";
    unsigned char vin[20] = {0};
    strncpy((char*)vin, vin_str.c_str(), sizeof(vin) - 1);
    
    std::cout << "Full VIN: " << vin_str << std::endl;
    std::cout << "VIN last 5 chars: " << vin_str.substr(vin_str.length() - 5) << std::endl;
    
    // Show the VIN bytes that GetSc will use (positions 12-16)
    std::cout << "VIN bytes [12-16]: ";
    for (int i = 12; i < 17; i++) {
        std::cout << (char)vin[i];
    }
    std::cout << " (";
    for (int i = 12; i < 17; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)vin[i];
        if (i < 16) std::cout << " ";
    }
    std::cout << std::dec << ")" << std::endl;
    
    // Call GetSc function
    unsigned char* sc = GetSc_Standalone(vin);
    if (sc) {
        printHex("Generated SC", sc, 3);  // GetSc returns 3 bytes
        
        // Check if result is non-zero
        bool nonzero = false;
        for (int i = 0; i < 3; i++) {
            if (sc[i] != 0) {
                nonzero = true;
                break;
            }
        }
        
        if (nonzero) {
            std::cout << "? SUCCESS: GetSc produced non-zero result!" << std::endl;
        } else {
            std::cout << "??  WARNING: GetSc returned all zeros" << std::endl;
        }
        
        delete[] sc;
    } else {
        std::cout << "? GetSc returned null" << std::endl;
    }
    
    // Test Case 2: Different VIN
    std::cout << "\n--- Test Case 2: Different VIN ---" << std::endl;
    
    std::string vin2_str = "LSJW26N93JS123456";
    unsigned char vin2[20] = {0};
    strncpy((char*)vin2, vin2_str.c_str(), sizeof(vin2) - 1);
    
    std::cout << "Full VIN: " << vin2_str << std::endl;
    std::cout << "VIN last 5 chars: " << vin2_str.substr(vin2_str.length() - 5) << std::endl;
    
    unsigned char* sc2 = GetSc_Standalone(vin2);
    if (sc2) {
        printHex("Generated SC", sc2, 3);
        
        bool nonzero = false;
        for (int i = 0; i < 3; i++) {
            if (sc2[i] != 0) {
                nonzero = true;
                break;
            }
        }
        
        if (nonzero) {
            std::cout << "? SUCCESS: GetSc produced non-zero result!" << std::endl;
        } else {
            std::cout << "??  WARNING: GetSc returned all zeros" << std::endl;
        }
        
        delete[] sc2;
    } else {
        std::cout << "? GetSc returned null" << std::endl;
    }
    
    // Test Case 3: Simple test cases
    std::cout << "\n--- Test Case 3: Simple Test Cases ---" << std::endl;
    
    const char* test_vins[] = {
        "ABCDEFGHIJKLMNOPQRS",  // Last 5: NOPQR
        "12345678901234567890", // Last 5: 67890
        "AAAAAAAAAAAAAAAAAAAA"  // Last 5: AAAAA
    };
    
    for (int t = 0; t < 3; t++) {
        unsigned char test_vin[20] = {0};
        strncpy((char*)test_vin, test_vins[t], sizeof(test_vin) - 1);
        
        std::cout << "\nTest VIN: " << test_vins[t] << std::endl;
        std::cout << "Last 5 chars: ";
        for (int i = 12; i < 17; i++) {
            std::cout << (char)test_vin[i];
        }
        std::cout << std::endl;
        
        unsigned char* sc_test = GetSc_Standalone(test_vin);
        if (sc_test) {
            printHex("Generated SC", sc_test, 3);
            delete[] sc_test;
        } else {
            std::cout << "? GetSc returned null" << std::endl;
        }
    }
    
    // Test Case 4: Library function check
    std::cout << "\n--- Test Case 4: Library Function Check ---" << std::endl;
    
    HMODULE handle = LoadLibrary("./lib/libG1_alg1.so");
    if (handle) {
        std::cout << "? libG1_alg1.so loaded successfully" << std::endl;
        
        void* func = GetProcAddress(handle, "Trans_InputCode");
        if (func) {
            std::cout << "? Trans_InputCode function found" << std::endl;
        } else {
            std::cout << "? Trans_InputCode function not found" << std::endl;
            
            // Try alternative function names
            const char* alt_names[] = {"trans_inputcode", "TransInputCode", "G1_Trans_InputCode"};
            for (int i = 0; i < 3; i++) {
                void* alt_func = GetProcAddress(handle, alt_names[i]);
                if (alt_func) {
                    std::cout << "? Found alternative function: " << alt_names[i] << std::endl;
                    break;
                }
            }
        }
        
        FreeLibrary(handle);
    } else {
        std::cout << "? Failed to load libG1_alg1.so: " << dlerror() << std::endl;
    }
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Standalone GetSc test completed." << std::endl;
    std::cout << "This test verifies the core VIN to SC conversion logic." << std::endl;
    
    return 0;
}
