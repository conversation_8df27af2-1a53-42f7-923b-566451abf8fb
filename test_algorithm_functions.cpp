// Direct test of Algorithm.cpp functions
// This tests the actual functions implemented in Algorithm.cpp

#include <iostream>
#include <iomanip>
#include <cstring>
#include <dlfcn.h>

void printHex(const char* label, const unsigned char* data, int len) {
    std::cout << std::setw(12) << std::left << label << ": ";
    for (int i = 0; i < len; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)data[i];
    }
    std::cout << std::dec << std::endl;
}

// Declare the functions from Algorithm.cpp
extern "C" {
    unsigned char* GetSc_New(unsigned char* vinCode);
    unsigned char* GetIncode_New(unsigned char* outCode, unsigned char* sc);
    unsigned char* GetSc_2017(unsigned char* vinCode);
    unsigned char* GetIncode_2017(unsigned char* outCode, unsigned char* sc);
    unsigned long GetESCL(unsigned char* vinCode);
}

int main() {
    std::cout << "=== Direct Algorithm.cpp Function Test ===" << std::endl;
    
    // Test data - Windows tool case
    std::string vin_str = "LFPHC7CC6L1B75928";
    std::string outcode_str = "0A0E0C01";
    
    unsigned char vin[20] = {0};
    unsigned char outcode[10] = {0};
    
    // Copy VIN and OutCode
    strncpy((char*)vin, vin_str.c_str(), sizeof(vin) - 1);
    strncpy((char*)outcode, outcode_str.c_str(), sizeof(outcode) - 1);
    
    std::cout << "Input VIN: " << vin_str << std::endl;
    std::cout << "Input OutCode: " << outcode_str << std::endl;
    std::cout << "VIN last 5 chars: " << vin_str.substr(vin_str.length() - 5) << std::endl;
    
    // Test 1: GetSc_New (G2 2016 algorithm)
    std::cout << "\n--- Test 1: GetSc_New (G2 2016) ---" << std::endl;
    
    unsigned char* sc_new = GetSc_New(vin);
    if (sc_new) {
        printHex("SC (G2 2016)", sc_new, 16);
        
        // Test GetIncode_New
        unsigned char* incode_new = GetIncode_New(outcode, sc_new);
        if (incode_new) {
            printHex("Incode (G2)", incode_new, 16);
            
            // Check if results are non-zero
            bool sc_nonzero = false, incode_nonzero = false;
            for (int i = 0; i < 16; i++) {
                if (sc_new[i] != 0) sc_nonzero = true;
                if (incode_new[i] != 0) incode_nonzero = true;
            }
            
            if (sc_nonzero && incode_nonzero) {
                std::cout << "✅ G2 algorithm produced non-zero results" << std::endl;
            } else {
                std::cout << "⚠️  G2 algorithm returned zeros (library issue)" << std::endl;
            }
            
            delete[] incode_new;
        } else {
            std::cout << "❌ GetIncode_New returned null" << std::endl;
        }
        
        delete[] sc_new;
    } else {
        std::cout << "❌ GetSc_New returned null" << std::endl;
    }
    
    // Test 2: GetSc_2017 (2017 algorithm)
    std::cout << "\n--- Test 2: GetSc_2017 (2017 Algorithm) ---" << std::endl;
    
    unsigned char* sc_2017 = GetSc_2017(vin);
    if (sc_2017) {
        printHex("SC (2017)", sc_2017, 4);  // 2017 algorithm uses 4 bytes
        
        // Test GetIncode_2017
        unsigned char* incode_2017 = GetIncode_2017(outcode, sc_2017);
        if (incode_2017) {
            printHex("Incode (2017)", incode_2017, 4);  // 2017 algorithm uses 4 bytes
            
            // Check if results are non-zero
            bool sc_nonzero = false, incode_nonzero = false;
            for (int i = 0; i < 4; i++) {
                if (sc_2017[i] != 0) sc_nonzero = true;
                if (incode_2017[i] != 0) incode_nonzero = true;
            }
            
            if (sc_nonzero && incode_nonzero) {
                std::cout << "✅ 2017 algorithm produced non-zero results" << std::endl;
            } else {
                std::cout << "⚠️  2017 algorithm returned zeros (library issue)" << std::endl;
            }
            
            delete[] incode_2017;
        } else {
            std::cout << "❌ GetIncode_2017 returned null" << std::endl;
        }
        
        delete[] sc_2017;
    } else {
        std::cout << "❌ GetSc_2017 returned null" << std::endl;
    }
    
    // Test 3: GetESCL function
    std::cout << "\n--- Test 3: GetESCL ---" << std::endl;
    
    unsigned long escl = GetESCL(vin);
    std::cout << "ESCL: " << std::hex << std::setfill('0') << std::setw(8) << escl << std::dec << std::endl;
    
    if (escl != 0) {
        std::cout << "✅ ESCL function produced non-zero result" << std::endl;
    } else {
        std::cout << "⚠️  ESCL function returned zero" << std::endl;
    }
    
    // Test 4: Test with different VIN
    std::cout << "\n--- Test 4: Different VIN ---" << std::endl;
    
    std::string vin2_str = "LSJW26N93JS123456";
    unsigned char vin2[20] = {0};
    strncpy((char*)vin2, vin2_str.c_str(), sizeof(vin2) - 1);
    
    std::cout << "Input VIN: " << vin2_str << std::endl;
    
    unsigned char* sc2 = GetSc_New(vin2);
    if (sc2) {
        printHex("SC (VIN2)", sc2, 16);
        delete[] sc2;
    }
    
    // Test 5: Check library dependencies
    std::cout << "\n--- Test 5: Library Dependency Check ---" << std::endl;
    
    const char* libs[] = {
        "./lib/libG2_encryption.so",
        "./lib/libG1_alg1.so",
        "./lib/libG1_alg3.so",
        "./lib/libC303.so",
        "./lib/libencryptalg1.so",
        "./lib/libencryptalg2.so"
    };
    
    for (int i = 0; i < 6; i++) {
        void* handle = dlopen(libs[i], RTLD_LAZY);
        if (handle) {
            std::cout << "✅ " << libs[i] << " - Available" << std::endl;
            
            // Try to find specific functions
            if (strstr(libs[i], "G2_encryption")) {
                void* func1 = dlsym(handle, "G2_VinToSc");
                void* func2 = dlsym(handle, "G2_CalcInCode");
                std::cout << "   G2_VinToSc: " << (func1 ? "Found" : "Not found") << std::endl;
                std::cout << "   G2_CalcInCode: " << (func2 ? "Found" : "Not found") << std::endl;
            } else if (strstr(libs[i], "G1_alg3")) {
                void* func = dlsym(handle, "CalcInCode");
                std::cout << "   CalcInCode: " << (func ? "Found" : "Not found") << std::endl;
            }
            
            dlclose(handle);
        } else {
            std::cout << "❌ " << libs[i] << " - " << dlerror() << std::endl;
        }
    }
    
    std::cout << "\n=== Summary ===" << std::endl;
    std::cout << "This test directly calls Algorithm.cpp functions to verify:" << std::endl;
    std::cout << "1. Function availability and execution" << std::endl;
    std::cout << "2. Library loading and symbol resolution" << std::endl;
    std::cout << "3. Algorithm output validation" << std::endl;
    std::cout << "4. Dependency verification" << std::endl;
    
    return 0;
}
