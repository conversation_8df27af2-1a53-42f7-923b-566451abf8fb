#!/bin/bash

# Quick verification script for vendor libraries
# This provides a simple pass/fail check for each library

echo "=========================================="
echo "QUICK LIBRARY VERIFICATION"
echo "=========================================="
echo ""

# Function to check a library
check_library() {
    local lib=$1
    local expected_functions=$2
    
    echo "Checking: $lib"
    
    if [ ! -f "$lib" ]; then
        echo "  ? FAIL: Library file not found"
        return 1
    fi
    
    echo "  ? File exists"
    
    # Check if it's a valid ARM64 library
    if file "$lib" | grep -q "ARM aarch64"; then
        echo "  ? ARM64 architecture"
    else
        echo "  ? FAIL: Not ARM64 architecture"
        file "$lib"
        return 1
    fi
    
    # Check each expected function
    local all_functions_exported=true
    
    for func in $expected_functions; do
        echo "    Checking function: $func"
        
        # Check if function exists in strings
        if strings "$lib" | grep -q "^$func$"; then
            echo "      ? Found in strings"
        else
            echo "      ? NOT found in strings"
            all_functions_exported=false
            continue
        fi
        
        # Check if function is exported (most important test)
        if nm -D "$lib" 2>/dev/null | grep -q " T $func$\| T $func "; then
            echo "      ? EXPORTED as dynamic symbol"
        else
            echo "      ? NOT EXPORTED as dynamic symbol"
            all_functions_exported=false
            
            # Check if it exists as local symbol
            if nm "$lib" 2>/dev/null | grep -q " t $func$\| t $func "; then
                echo "      ??  Exists as LOCAL symbol (this is the problem!)"
            fi
        fi
    done
    
    if [ "$all_functions_exported" = true ]; then
        echo "  ? PASS: All functions properly exported"
        return 0
    else
        echo "  ? FAIL: Functions not properly exported"
        return 1
    fi
}

echo "Testing libraries..."
echo ""

# Test each library
total_libraries=0
passed_libraries=0

# libG2_encryption.so
total_libraries=$((total_libraries + 1))
if check_library "lib/libG2_encryption.so" "G2_VinToSc G2_CalcInCode"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

# libG1_alg1.so
total_libraries=$((total_libraries + 1))
if check_library "lib/libG1_alg1.so" "Trans_InputCode"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

# libG1_alg3.so
total_libraries=$((total_libraries + 1))
if check_library "lib/libG1_alg3.so" "CalcInCode"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

# libC303.so
total_libraries=$((total_libraries + 1))
if check_library "lib/libC303.so" "alg"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

# libencryptalg1.so
total_libraries=$((total_libraries + 1))
if check_library "lib/libencryptalg1.so" "EncryptAlg1"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

# libencryptalg2.so
total_libraries=$((total_libraries + 1))
if check_library "lib/libencryptalg2.so" "EncryptAlg2"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

# Final summary
echo "=========================================="
echo "FINAL RESULT"
echo "=========================================="
echo "Libraries tested: $total_libraries"
echo "Libraries passed: $passed_libraries"
echo "Libraries failed: $((total_libraries - passed_libraries))"
echo ""

if [ $passed_libraries -eq $total_libraries ]; then
    echo "? ALL LIBRARIES PASS - Ready for use!"
    exit 0
else
    echo "? SOME LIBRARIES FAIL - Need vendor fix!"
    echo ""
    echo "Please send the detailed report to vendor:"
    echo "- VENDOR_ISSUE_SUMMARY.md"
    echo "- library_symbol_export_issues_*.txt"
    exit 1
fi
