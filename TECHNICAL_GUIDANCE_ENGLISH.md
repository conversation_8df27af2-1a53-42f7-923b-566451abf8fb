# ARM64 Linux .so Library Compilation Technical Guide

## ? Objective
Generate shared library files that can be correctly dynamically loaded in ARM64 Linux environment, ensuring all public functions are accessible via dlsym().

## ? Library Files Requiring Fix

Based on your provided library files, here's the fix list:

| Library File | Expected Export Functions | Current Status | File Size |
|-------------|---------------------------|----------------|-----------|
| `libG1_alg1.so` | `Trans_InputCode` | ? Function exists but not exported | 12 KB |
| `libG1_alg3.so` | `CalcInCode` | ? Function exists but not exported | 8 KB |
| `libG1_alg4.so` | `AlgFunction` | ? Need to confirm function name | 8 KB |
| `libG2Encryption.so` | `G2_VinToSc`, `G2_CalcInCode` | ? Functions exist but not exported | 21 KB |
| `libRandom.so` | `RandomFunction` | ? Need to confirm function name | 8 KB |

## ? Compiler Configuration

### 1. Basic Compilation Command Template
```bash
# Correct compilation command (use this command)
gcc -shared -fPIC -fvisibility=default -o libXXX.so *.o

# Specific example
gcc -shared -fPIC -fvisibility=default -o libG2Encryption.so G2Encryption.o
```

### 2. Key Compilation Parameters Explanation
- `-shared`: Generate shared library file
- `-fPIC`: Generate Position Independent Code (required)
- `-fvisibility=default`: Export all symbols by default (**KEY parameter, solves current issue**)

### 3. ARM64 Architecture Specific Settings
```bash
# Ensure compilation for ARM64 architecture
gcc -march=armv8-a -mtune=cortex-a72 -shared -fPIC -fvisibility=default -o libXXX.so *.o

# If using cross-compilation toolchain
aarch64-linux-gnu-gcc -shared -fPIC -fvisibility=default -o libXXX.so *.o
```

## ? Source Code Modification Guide

### 1. Function Declaration Methods (Recommended)
```c
// Method 1: Using attribute declaration (strongly recommended)
__attribute__((visibility("default"))) 
void G2_VinToSc(const unsigned char* vin, unsigned char* sc);

__attribute__((visibility("default"))) 
void G2_CalcInCode(const unsigned char* outcode, const unsigned char* sc, unsigned char* incode);

// Method 2: Using macro definition (convenient for management)
#define EXPORT __attribute__((visibility("default")))

EXPORT void G2_VinToSc(const unsigned char* vin, unsigned char* sc);
EXPORT void G2_CalcInCode(const unsigned char* outcode, const unsigned char* sc, unsigned char* incode);
```

### 2. Header File Configuration Example
```c
// G2Encryption.h header file example
#ifndef G2_ENCRYPTION_H
#define G2_ENCRYPTION_H

#ifdef __cplusplus
extern "C" {
#endif

// Ensure functions can be called externally (important)
__attribute__((visibility("default"))) 
void G2_VinToSc(const unsigned char* vin, unsigned char* sc);

__attribute__((visibility("default"))) 
void G2_CalcInCode(const unsigned char* outcode, const unsigned char* sc, unsigned char* incode);

#ifdef __cplusplus
}
#endif

#endif // G2_ENCRYPTION_H
```

### 3. Implementation File Notes
```c
// G2Encryption.c implementation file example
#include "G2Encryption.h"

// When implementing functions note: do not use static keyword
void G2_VinToSc(const unsigned char* vin, unsigned char* sc) {
    // Your algorithm implementation code
}

void G2_CalcInCode(const unsigned char* outcode, const unsigned char* sc, unsigned char* incode) {
    // Your algorithm implementation code
}
```

## ?? Makefile Configuration Template

### Complete Makefile Example
```makefile
# ARM64 Linux shared library Makefile configuration

CC = gcc
CFLAGS = -fPIC -fvisibility=default -Wall -O2 -march=armv8-a
LDFLAGS = -shared

# Architecture verification
ARCH := $(shell uname -m)
ifneq ($(ARCH),aarch64)
    $(warning Warning: Not on ARM64 environment, please confirm cross-compilation settings are correct)
endif

# Target library file list
LIBRARIES = libG1_alg1.so libG1_alg3.so libG1_alg4.so libG2Encryption.so libRandom.so

all: $(LIBRARIES)

# G1 Algorithm Library 1
libG1_alg1.so: G1_alg1.o
	$(CC) $(LDFLAGS) -o $@ $^

G1_alg1.o: G1_alg1.c
	$(CC) $(CFLAGS) -c $< -o $@

# G2 Encryption Library
libG2Encryption.so: G2Encryption.o
	$(CC) $(LDFLAGS) -o $@ $^

G2Encryption.o: G2Encryption.c G2Encryption.h
	$(CC) $(CFLAGS) -c $< -o $@

# Verification target
verify: $(LIBRARIES)
	@echo "Verifying library files..."
	@for lib in $(LIBRARIES); do \
		echo "Checking $$lib:"; \
		file $$lib; \
		nm -D $$lib | grep -E "(G2_|Trans_|CalcInCode|AlgFunction|RandomFunction)" || echo "No exported functions found"; \
		echo ""; \
	done

clean:
	rm -f *.o $(LIBRARIES)

.PHONY: all verify clean
```

## ? Verification Steps

### 1. Immediate Verification After Compilation
```bash
# Check file type and architecture
file libG2Encryption.so
# Should display: ELF 64-bit LSB shared object, ARM aarch64

# Check exported symbols (most important verification)
nm -D libG2Encryption.so
# Should display function symbols, for example:
# 0000000000001960 T G2_VinToSc
# 0000000000000f90 T G2_CalcInCode

# Check dynamic symbol table
objdump -T libG2Encryption.so
# Should display functions in DYNAMIC SYMBOL TABLE
```

### 2. Runtime Loading Verification
```c
// test_loading.c - Verify dynamic loading success
#include <stdio.h>
#include <dlfcn.h>

int main() {
    void* handle = dlopen("./libG2Encryption.so", RTLD_LAZY);
    if (!handle) {
        printf("Load failed: %s\n", dlerror());
        return 1;
    }
    
    void* func = dlsym(handle, "G2_VinToSc");
    if (!func) {
        printf("Function not found: %s\n", dlerror());
        dlclose(handle);
        return 1;
    }
    
    printf("Success: Function found and accessible\n");
    dlclose(handle);
    return 0;
}
```

Compile and run verification:
```bash
gcc -o test_loading test_loading.c -ldl
./test_loading
```

## ? Common Issues and Solutions

### 1. Function Exists But Inaccessible
**Issue**: `strings` can find function name, but `dlsym()` fails
**Cause**: Function compiled as local symbol ('t') instead of global symbol ('T')
**Solution**: Add `-fvisibility=default` compilation parameter or use `__attribute__((visibility("default")))`

### 2. Architecture Mismatch
**Issue**: Library compiled on x86-64 cannot run on ARM64
**Solution**: Use ARM64 compilation environment or correctly configure cross-compilation toolchain

### 3. Library Dependency Issues
**Issue**: Library depends on other libraries but cannot find them at runtime
**Solution**: 
```bash
# Check library dependencies
ldd libG2Encryption.so

# Set library search path
export LD_LIBRARY_PATH=/path/to/libs:$LD_LIBRARY_PATH
```

## ? Delivery Acceptance Checklist

Vendor must ensure when delivering:

- [ ] All library files are ARM64 architecture (verify with `file` command)
- [ ] All expected functions in dynamic symbol table (verify with `nm -D`)
- [ ] Functions marked as global symbol 'T' not local symbol 't'
- [ ] All functions successfully accessible via `dlsym()`
- [ ] Provide corresponding header files
- [ ] Provide simple test program to verify functionality

## ? Debugging Tools Usage

```bash
# Symbol analysis tools
nm -D libXXX.so              # View exported symbols
nm libXXX.so                 # View all symbols
objdump -T libXXX.so         # View dynamic symbol table
readelf -Ws libXXX.so        # Detailed symbol information

# Dependency analysis tools
ldd libXXX.so                # View dependent libraries
objdump -p libXXX.so         # View program header information

# String analysis tools
strings libXXX.so | grep function_name  # Find function names
```

## ? Technical Support Contact

If you encounter issues, please provide:
1. Complete compilation command and output results
2. Output results of `nm -D` and `objdump -T`
3. Function declaration parts in source code
4. Compilation environment information (`gcc --version`, `uname -a`)

## ? Important Reminder

**Root cause of current issue**: All functions exist in library files but are not correctly exported as dynamic symbols, making them inaccessible via dlsym() at runtime.

**Core solution**: Add `-fvisibility=default` parameter during compilation, or use `__attribute__((visibility("default")))` to declare functions in source code.

**Verification standard**: Using `nm -D libXXX.so` command must show all expected function symbols.
