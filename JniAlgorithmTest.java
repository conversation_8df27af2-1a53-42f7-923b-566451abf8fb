// 
package com.launch.chyc.jni;

public class JniAlgorithm {
    
    static {
        try {
            System.load(System.getProperty("user.dir") + "/bin/libJniAlgorithm.so");
            System.out.println("? Successfully loaded libJniAlgorithm.so");
        } catch (UnsatisfiedLinkError e) {
            System.err.println("? Failed to load libJniAlgorithm.so: " + e.getMessage());
            System.exit(1);
        }
    }

    //
    public native void getScAndIncodeG2(String vin, String out, byte[] sc16, byte[] incode16);

    public static void main(String[] args) {
        System.out.println("=== Final JNI Test on ARM64 ===");
        
        JniAlgorithm test = new JniAlgorithm();
        
        // 
        String vin = "LSJW26N93JS123456";  // 
        String outCode = "A1B2C3";         //
        
        byte[] sc16 = new byte[16];
        byte[] incode16 = new byte[16];
        
        System.out.println("Input VIN: " + vin);
        System.out.println("Input OutCode: " + outCode);
        System.out.println("VIN last 5 chars: " + vin.substring(vin.length() - 5));
        
        try {
            System.out.println("\nCalling JNI method...");
            
            //
            test.getScAndIncodeG2(vin, outCode, sc16, incode16);
            
            System.out.println("? JNI method completed successfully!");
            
            //
            System.out.print("SC16:     ");
            for (byte b : sc16) {
                System.out.printf("%02X", b & 0xFF);
            }
            System.out.println();
            
            System.out.print("Incode16: ");
            for (byte b : incode16) {
                System.out.printf("%02X", b & 0xFF);
            }
            System.out.println();
            
            //
            boolean scAllZero = true;
            boolean incodeAllZero = true;
            
            for (byte b : sc16) {
                if (b != 0) {
                    scAllZero = false;
                    break;
                }
            }
            
            for (byte b : incode16) {
                if (b != 0) {
                    incodeAllZero = false;
                    break;
                }
            }
            
            System.out.println("\n=== Results Analysis ===");
            if (scAllZero) {
                System.out.println("??  WARNING: SC16 is all zeros!");
            } else {
                System.out.println("? SUCCESS: SC16 has non-zero values");
            }
            
            if (incodeAllZero) {
                System.out.println("??  WARNING: Incode16 is all zeros!");
            } else {
                System.out.println("? SUCCESS: Incode16 has non-zero values");
            }
            
            //
            System.out.println("\n=== Testing with different data ===");
            String vin2 = "LFPHC7CC6L1B75928";
            String outCode2 = "0A0E0C";
            
            byte[] sc16_2 = new byte[16];
            byte[] incode16_2 = new byte[16];
            
            System.out.println("Input VIN: " + vin2);
            System.out.println("Input OutCode: " + outCode2);
            System.out.println("VIN last 5 chars: " + vin2.substring(vin2.length() - 5));
            
            test.getScAndIncodeG2(vin2, outCode2, sc16_2, incode16_2);
            
            System.out.print("SC16:     ");
            for (byte b : sc16_2) {
                System.out.printf("%02X", b & 0xFF);
            }
            System.out.println();
            
            System.out.print("Incode16: ");
            for (byte b : incode16_2) {
                System.out.printf("%02X", b & 0xFF);
            }
            System.out.println();
            
            System.out.println("\n? ALL TESTS COMPLETED SUCCESSFULLY!");
            System.out.println("The JNI integration is working correctly!");
            System.out.println("You can now replace the test G2 algorithm with the real implementation.");
            
        } catch (Exception e) {
            System.err.println("? Error calling JNI method: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== Test completed ===");
        System.out.println("Check /tmp/jni_algorithm.log for detailed logs.");
    }
}
