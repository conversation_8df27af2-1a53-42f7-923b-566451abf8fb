// Direct C++ test for libG2Algorithm.so
// This tests the library without JNI layer

#include <iostream>
#include <iomanip>
#include <cstring>
#include <dlfcn.h>

void printHex(const char* label, const unsigned char* data, int len) {
    std::cout << std::setw(12) << std::left << label << ": ";
    for (int i = 0; i < len; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)data[i];
    }
    std::cout << std::dec << std::endl;
}

int main() {
    std::cout << "=== Direct C++ Test for libG2Algorithm.so ===" << std::endl;
    
    // Test 1: Load the library
    std::cout << "\n--- Test 1: Library Loading ---" << std::endl;
    
    void* handle = dlopen("./lib/libG2Algorithm.so", RTLD_LAZY);
    if (!handle) {
        std::cerr << "? Failed to load libG2Algorithm.so: " << dlerror() << std::endl;
        return 1;
    }
    
    std::cout << "? Successfully loaded libG2Algorithm.so" << std::endl;
    
    // Test 2: Try to find G2 functions
    std::cout << "\n--- Test 2: Function Symbol Resolution ---" << std::endl;
    
    // Try different possible function names
    const char* function_names[] = {
        "G2_VinToSc",
        "G2_CalcInCode", 
        "G2VinToSc",
        "G2CalcInCode",
        "VinToSc",
        "CalcInCode",
        "g2_VinToSc",
        "g2_CalcInCode"
    };
    
    void* found_functions[8] = {nullptr};
    int found_count = 0;
    
    for (int i = 0; i < 8; i++) {
        void* func = dlsym(handle, function_names[i]);
        if (func) {
            std::cout << "? Found function: " << function_names[i] << std::endl;
            found_functions[i] = func;
            found_count++;
        } else {
            std::cout << "? Not found: " << function_names[i] << std::endl;
        }
        // Clear error
        dlerror();
    }
    
    if (found_count == 0) {
        std::cout << "??  No G2 functions found via dlsym" << std::endl;
        std::cout << "This may indicate symbol export issues" << std::endl;
    } else {
        std::cout << "? Found " << found_count << " G2 functions" << std::endl;
    }
    
    // Test 3: If functions found, try to call them
    if (found_functions[0] && found_functions[1]) { // G2_VinToSc and G2_CalcInCode
        std::cout << "\n--- Test 3: Function Call Test ---" << std::endl;
        
        typedef void (*G2_VinToSc_t)(const unsigned char*, unsigned char*);
        typedef void (*G2_CalcInCode_t)(const unsigned char*, const unsigned char*, unsigned char*);
        
        G2_VinToSc_t G2_VinToSc = (G2_VinToSc_t)found_functions[0];
        G2_CalcInCode_t G2_CalcInCode = (G2_CalcInCode_t)found_functions[1];
        
        // Test data
        unsigned char vin[5] = {'7', '5', '9', '2', '8'}; // "75928"
        unsigned char outcode[4] = {0x0A, 0x0E, 0x0C, 0x01};
        unsigned char sc[32] = {0};  // Use 32 bytes as per vendor spec
        unsigned char incode[32] = {0};  // Use 32 bytes as per vendor spec
        
        try {
            // Call G2_VinToSc
            G2_VinToSc(vin, sc);
            std::cout << "? G2_VinToSc called successfully" << std::endl;
            
            // Call G2_CalcInCode
            G2_CalcInCode(outcode, sc, incode);
            std::cout << "? G2_CalcInCode called successfully" << std::endl;
            
            // Display results
            printHex("VIN", vin, 5);
            printHex("OutCode", outcode, 4);
            printHex("SC", sc, 16);  // Show first 16 bytes
            printHex("InCode", incode, 16);  // Show first 16 bytes
            
            // Check if results are non-zero
            bool sc_nonzero = false, incode_nonzero = false;
            for (int i = 0; i < 16; i++) {
                if (sc[i] != 0) sc_nonzero = true;
                if (incode[i] != 0) incode_nonzero = true;
            }
            
            if (sc_nonzero && incode_nonzero) {
                std::cout << "? SUCCESS: Both SC and InCode have non-zero values!" << std::endl;
            } else {
                std::cout << "??  WARNING: Some results are zero" << std::endl;
                std::cout << "SC non-zero: " << (sc_nonzero ? "Yes" : "No") << std::endl;
                std::cout << "InCode non-zero: " << (incode_nonzero ? "Yes" : "No") << std::endl;
            }
            
            // Compare with expected Windows tool result
            unsigned char expected_sc[16] = {0xD6, 0x08, 0xCE, 0x5D, 0x5A, 0xC6, 0x3D, 0xF6, 
                                            0x8E, 0xD6, 0xAA, 0x97, 0x66, 0x50, 0xD0, 0x11};
            
            std::cout << "\nComparison with Windows tool:" << std::endl;
            printHex("Expected SC", expected_sc, 16);
            
            if (memcmp(sc, expected_sc, 16) == 0) {
                std::cout << "? PERFECT MATCH with Windows tool!" << std::endl;
            } else {
                std::cout << "??  Different from Windows tool (may be normal with different implementation)" << std::endl;
            }
            
        } catch (...) {
            std::cout << "? Exception occurred during function calls" << std::endl;
        }
    }
    
    // Test 4: Library info
    std::cout << "\n--- Test 4: Library Information ---" << std::endl;
    
    // Check library file info
    system("echo 'File info:' && file lib/libG2Algorithm.so");
    system("echo 'File size:' && ls -lh lib/libG2Algorithm.so");
    
    // Check symbols
    std::cout << "\nSymbol analysis:" << std::endl;
    system("echo 'Dynamic symbols:' && nm -D lib/libG2Algorithm.so 2>/dev/null | head -10");
    system("echo 'G2 related symbols:' && nm lib/libG2Algorithm.so 2>/dev/null | grep -i g2");
    
    dlclose(handle);
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "Library loading: ? Success" << std::endl;
    std::cout << "Function resolution: " << (found_count > 0 ? "? Success" : "? Failed") << std::endl;
    std::cout << "Algorithm execution: " << (found_count > 1 ? "? Tested" : "? Not tested") << std::endl;
    
    return 0;
}
