#!/bin/bash

# Quick Library Verification Script - Fixed Paths
# Fast check for vendor library symbol export issues

echo "=========================================="
echo "QUICK LIBRARY VERIFICATION SCRIPT"
echo "Checking ARM64 Linux shared library symbol export issues"
echo "=========================================="
echo ""

# Function to check a library
check_library() {
    local lib=$1
    local expected_functions=$2
    
    echo "Checking library: $lib"
    
    if [ ! -f "$lib" ]; then
        echo "  ? FAIL: Library file not found"
        return 1
    fi
    
    echo "  ? File exists"
    
    # Check architecture
    if file "$lib" | grep -q "ARM aarch64"; then
        echo "  ? ARM64 architecture correct"
    else
        echo "  ? FAIL: Not ARM64 architecture"
        file "$lib"
        return 1
    fi
    
    # Check each expected function
    local all_functions_exported=true
    
    for func in $expected_functions; do
        echo "    Checking function: $func"
        
        # Check if function exists in strings
        if strings "$lib" | grep -q "^$func$"; then
            echo "      ? Found in strings"
        else
            echo "      ? NOT found in strings"
            all_functions_exported=false
            continue
        fi
        
        # Check if function is exported (most important test)
        if nm -D "$lib" 2>/dev/null | grep -q " T $func$\| T $func "; then
            echo "      ? EXPORTED as dynamic symbol"
        else
            echo "      ? NOT EXPORTED as dynamic symbol"
            all_functions_exported=false
            
            # Check if it exists as local symbol
            if nm "$lib" 2>/dev/null | grep -q " t $func$\| t $func "; then
                echo "      ??  Exists as LOCAL symbol (this is the problem!)"
            fi
        fi
    done
    
    if [ "$all_functions_exported" = true ]; then
        echo "  ? PASS: All functions properly exported"
        return 0
    else
        echo "  ? FAIL: Functions not properly exported"
        return 1
    fi
}

echo "Starting library tests..."
echo ""

# Test counters
total_libraries=0
passed_libraries=0

# Test each library (corrected paths)
echo "1. lib/libG1_alg1.so"
total_libraries=$((total_libraries + 1))
if check_library "lib/libG1_alg1.so" "Trans_InputCode"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

echo "2. lib/libG1_alg3.so"
total_libraries=$((total_libraries + 1))
if check_library "lib/libG1_alg3.so" "CalcInCode"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

echo "3. lib/libG1_alg4.so"
total_libraries=$((total_libraries + 1))
if check_library "lib/libG1_alg4.so" "AlgFunction"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

echo "4. lib/libG2Encryption.so"
total_libraries=$((total_libraries + 1))
if check_library "lib/libG2Encryption.so" "G2_VinToSc G2_CalcInCode"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

echo "5. lib/libRandom.so"
total_libraries=$((total_libraries + 1))
if check_library "lib/libRandom.so" "RandomFunction"; then
    passed_libraries=$((passed_libraries + 1))
fi
echo ""

# Final results
echo "=========================================="
echo "VERIFICATION SUMMARY"
echo "=========================================="
echo "Libraries tested: $total_libraries"
echo "Libraries passed: $passed_libraries"
echo "Libraries failed: $((total_libraries - passed_libraries))"
echo ""

if [ $passed_libraries -eq $total_libraries ]; then
    echo "? ALL LIBRARIES PASS - Ready for use!"
    echo ""
    echo "Delivery checklist confirmed:"
    echo "? All libraries are ARM64 architecture"
    echo "? All functions are properly exported"
    echo "? Functions accessible via dlsym()"
    exit 0
else
    echo "? SOME LIBRARIES FAIL - Need vendor fix!"
    echo ""
    echo "Fix recommendations:"
    echo "1. Add -fvisibility=default to compilation"
    echo "2. Use __attribute__((visibility(\"default\"))) in source code"
    echo "3. Ensure functions are not declared as static"
    echo "4. Use provided Makefile example for recompilation"
    echo ""
    echo "Send these documents to vendor:"
    echo "- Technical guidance documentation"
    echo "- Problem summary report"
    echo "- Vendor verification script"
    exit 1
fi
