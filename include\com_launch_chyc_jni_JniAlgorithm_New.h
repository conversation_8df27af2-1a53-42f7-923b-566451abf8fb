/* Header for class com_launch_chyc_jni_JniAlgorithm */
/* Updated to match new Algorithm.cpp functions */

#include <jni.h>

#ifndef _Included_com_launch_chyc_jni_JniAlgorithm
#define _Included_com_launch_chyc_jni_JniAlgorithm
#ifdef __cplusplus
extern "C" {
#endif

/*
 * Class:     com_launch_chyc_jni_JniAlgorithm
 * Method:    getScAndIncode
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScAndIncode
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject);

/*
 * Class:     com_launch_chyc_jni_JniAlgorithm
 * Method:    getScIncodeAndESCL
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScIncodeAndESCL
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject, jobject);

/*
 * Class:     com_launch_chyc_jni_JniAlgorithm
 * Method:    getScAndIncodeNew
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScAndIncodeNew
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject);

/*
 * Class:     com_launch_chyc_jni_JniAlgorithm
 * Method:    getScIncodeAndESCLNew
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)V
 */
JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScIncodeAndESCLNew
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject, jobject);

/*
 * Class:     com_launch_chyc_jni_JniAlgorithm
 * Method:    getScAndIncodeMain
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;I)V
 */
JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScAndIncodeMain
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject, jint);

/*
 * Class:     com_launch_chyc_jni_JniAlgorithm
 * Method:    getScIncodeAndESCLMain
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;I)V
 */
JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScIncodeAndESCLMain
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject, jobject, jint);

#ifdef __cplusplus
}
#endif
#endif
