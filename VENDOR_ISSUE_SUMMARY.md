# 供应商库符号导出问题总结

## 🚨 问题概述

所有提供的ARM64版本的.so库文件都存在**符号导出问题**，导致库文件无法正常使用。

## 📋 受影响的库文件

| 库文件 | 期望函数 | 状态 |
|--------|----------|------|
| `libG2_encryption.so` | `G2_VinToSc`, `G2_CalcInCode` | ❌ 函数存在但未导出 |
| `libG1_alg1.so` | `Trans_InputCode` | ❌ 函数存在但未导出 |
| `libG1_alg3.so` | `CalcInCode` | ❌ 函数存在但未导出 |
| `libC303.so` | `alg` | ❌ 文件缺失 |
| `libencryptalg1.so` | `EncryptAlg1` | ❌ 文件缺失 |
| `libencryptalg2.so` | `EncryptAlg2` | ❌ 文件缺失 |

## 🔍 技术细节

### 问题表现
1. **库可以加载**: `dlopen()` 成功
2. **函数无法找到**: `dlsym()` 失败，返回NULL
3. **函数确实存在**: `strings` 命令可以找到函数名
4. **符号表问题**: 函数在符号表中但未标记为导出

### 验证命令

```bash
# 检查导出的动态符号（应该显示函数，但实际没有）
nm -D libG2_encryption.so

# 检查所有符号（可以找到函数，但标记为本地符号't'而不是全局符号'T'）
nm libG2_encryption.so | grep G2

# 检查字符串（可以找到函数名）
strings libG2_encryption.so | grep G2
```

### 实际结果对比

**期望结果**（正确的库应该显示）:
```
$ nm -D libG2_encryption.so
0000000000001960 T G2_VinToSc
0000000000000f90 T G2_CalcInCode
```

**实际结果**（当前有问题的库）:
```
$ nm -D libG2_encryption.so
                 w __cxa_finalize@GLIBC_2.17
                 w __gmon_start__
                 w _ITM_deregisterTMCloneTable
                 w _ITM_registerTMCloneTable
                 U __stack_chk_fail@GLIBC_2.17
                 U __stack_chk_guard@GLIBC_2.17
```

**函数存在但未导出**:
```
$ nm libG2_encryption.so | grep G2
0000000000000f90 t G2_CalcInCode    # 't' = 本地符号，未导出
0000000000000e70 t G2_Secrecy_Module
0000000000001960 t G2_VinToSc       # 't' = 本地符号，未导出
```

## 💥 影响

1. **Algorithm.cpp无法工作**: 所有算法函数返回零值
2. **JNI集成失败**: Java调用返回空结果
3. **项目无法部署**: 核心功能不可用

## 🛠️ 解决方案

### 供应商需要修复

1. **重新编译库文件**，确保函数正确导出：
   ```bash
   # 编译时使用正确的可见性设置
   gcc -shared -fPIC -fvisibility=default -o libG2_encryption.so *.o
   ```

2. **在源代码中正确声明函数**：
   ```c
   // 确保函数声明为可导出
   __attribute__((visibility("default"))) 
   void G2_VinToSc(const unsigned char* vin, unsigned char* sc);
   ```

3. **验证修复**：
   ```bash
   # 确保这些命令显示期望的函数
   nm -D libG2_encryption.so | grep G2
   objdump -T libG2_encryption.so | grep G2
   ```

### 验证清单

供应商在交付前应确认：

- [ ] `nm -D <library>.so` 显示所有期望的函数
- [ ] `objdump -T <library>.so` 在DYNAMIC SYMBOL TABLE中显示函数
- [ ] 函数符号标记为 'T'（全局）而不是 't'（本地）
- [ ] `dlsym()` 可以成功找到函数

## 📞 联系信息

如需技术支持或澄清，请联系：
- 项目: JNI Algorithm Integration
- 平台: ARM64 Linux
- 日期: $(date)

## 📎 附件

详细的技术证据请参考自动生成的报告文件：`library_symbol_export_issues_*.txt`
