#!/bin/bash

# Vendor Library Verification Script - English Version
# This script should be run by the vendor to verify their libraries before delivery

echo "=========================================="
echo "VENDOR LIBRARY VERIFICATION SCRIPT"
echo "ARM64 Linux Shared Library Verification"
echo "=========================================="
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "PASS")
            echo -e "${GREEN}? PASS${NC}: $message"
            ;;
        "FAIL")
            echo -e "${RED}? FAIL${NC}: $message"
            ;;
        "WARN")
            echo -e "${YELLOW}??  WARN${NC}: $message"
            ;;
        "INFO")
            echo -e "??  INFO: $message"
            ;;
    esac
}

# Check if running on ARM64
check_architecture() {
    echo "Checking build environment..."
    local arch=$(uname -m)
    if [ "$arch" = "aarch64" ]; then
        print_status "PASS" "Running on ARM64 architecture"
        return 0
    else
        print_status "WARN" "Not running on ARM64 (current: $arch). Ensure cross-compilation is correct."
        return 1
    fi
}

# Verify a single library
verify_library() {
    local lib_file=$1
    local expected_functions=$2
    local lib_name=$(basename "$lib_file")
    
    echo ""
    echo "Verifying: $lib_name"
    echo "----------------------------------------"
    
    # Check if file exists
    if [ ! -f "$lib_file" ]; then
        print_status "FAIL" "Library file not found: $lib_file"
        return 1
    fi
    
    print_status "PASS" "Library file exists"
    
    # Check file type and architecture
    local file_info=$(file "$lib_file")
    if echo "$file_info" | grep -q "ARM aarch64"; then
        print_status "PASS" "Correct ARM64 architecture"
    else
        print_status "FAIL" "Incorrect architecture: $file_info"
        return 1
    fi
    
    if echo "$file_info" | grep -q "shared object"; then
        print_status "PASS" "Correct shared object type"
    else
        print_status "FAIL" "Not a shared object: $file_info"
        return 1
    fi
    
    # Check each expected function
    local all_functions_ok=true
    
    for func in $expected_functions; do
        echo "  Checking function: $func"
        
        # Check if function exists in strings
        if strings "$lib_file" | grep -q "^$func$"; then
            print_status "PASS" "    Function name found in binary"
        else
            print_status "FAIL" "    Function name not found in binary"
            all_functions_ok=false
            continue
        fi
        
        # Check if function is in symbol table
        if nm "$lib_file" 2>/dev/null | grep -q " [Tt] $func$"; then
            print_status "PASS" "    Function found in symbol table"
        else
            print_status "FAIL" "    Function not found in symbol table"
            all_functions_ok=false
            continue
        fi
        
        # Most important: Check if function is exported as dynamic symbol
        if nm -D "$lib_file" 2>/dev/null | grep -q " T $func$"; then
            print_status "PASS" "    Function EXPORTED as dynamic symbol (CRITICAL)"
        else
            print_status "FAIL" "    Function NOT EXPORTED as dynamic symbol (CRITICAL ERROR)"
            all_functions_ok=false
            
            # Additional diagnostic
            if nm "$lib_file" 2>/dev/null | grep -q " t $func$"; then
                print_status "FAIL" "    Function exists as LOCAL symbol - needs visibility fix"
            fi
        fi
    done
    
    # Runtime loading test
    echo "  Testing runtime loading..."
    
    # Create a simple test program
    cat > test_runtime_$$.c << EOF
#include <stdio.h>
#include <dlfcn.h>

int main() {
    void* handle = dlopen("$lib_file", RTLD_LAZY);
    if (!handle) {
        printf("FAIL: Cannot load library: %s\\n", dlerror());
        return 1;
    }
    
EOF
    
    for func in $expected_functions; do
        cat >> test_runtime_$$.c << EOF
    void* ${func}_ptr = dlsym(handle, "$func");
    if (!${func}_ptr) {
        printf("FAIL: Cannot find function $func: %s\\n", dlerror());
        dlclose(handle);
        return 1;
    }
    printf("PASS: Function $func found via dlsym\\n");
    
EOF
    done
    
    cat >> test_runtime_$$.c << EOF
    dlclose(handle);
    printf("PASS: All functions accessible via dlsym\\n");
    return 0;
}
EOF
    
    # Compile and run the test
    if gcc -o test_runtime_$$ test_runtime_$$.c -ldl 2>/dev/null; then
        if ./test_runtime_$$; then
            print_status "PASS" "Runtime loading test successful"
        else
            print_status "FAIL" "Runtime loading test failed"
            all_functions_ok=false
        fi
        rm -f test_runtime_$$ test_runtime_$$.c
    else
        print_status "WARN" "Could not compile runtime test"
        rm -f test_runtime_$$.c
    fi
    
    if [ "$all_functions_ok" = true ]; then
        print_status "PASS" "Library $lib_name verification SUCCESSFUL"
        return 0
    else
        print_status "FAIL" "Library $lib_name verification FAILED"
        return 1
    fi
}

# Main verification process
main() {
    echo "Starting library verification process..."
    echo ""
    
    # Check architecture
    check_architecture
    echo ""
    
    # Define libraries and their expected functions
    declare -A LIBRARIES
    LIBRARIES["libG1_alg1.so"]="Trans_InputCode"
    LIBRARIES["libG1_alg3.so"]="CalcInCode"
    LIBRARIES["libG1_alg4.so"]="AlgFunction"
    LIBRARIES["libG2Encryption.so"]="G2_VinToSc G2_CalcInCode"
    LIBRARIES["libRandom.so"]="RandomFunction"
    
    local total_libraries=0
    local passed_libraries=0
    
    # Verify each library
    for lib in "${!LIBRARIES[@]}"; do
        total_libraries=$((total_libraries + 1))
        if verify_library "$lib" "${LIBRARIES[$lib]}"; then
            passed_libraries=$((passed_libraries + 1))
        fi
    done
    
    # Final summary
    echo ""
    echo "=========================================="
    echo "VERIFICATION SUMMARY"
    echo "=========================================="
    echo "Total libraries: $total_libraries"
    echo "Passed: $passed_libraries"
    echo "Failed: $((total_libraries - passed_libraries))"
    echo ""
    
    if [ $passed_libraries -eq $total_libraries ]; then
        print_status "PASS" "ALL LIBRARIES VERIFIED SUCCESSFULLY"
        echo ""
        echo "? Libraries are ready for delivery!"
        echo ""
        echo "Delivery checklist:"
        echo "- ? All libraries are ARM64 architecture"
        echo "- ? All functions are properly exported"
        echo "- ? Runtime loading works correctly"
        echo "- ? dlsym() can find all functions"
        return 0
    else
        print_status "FAIL" "SOME LIBRARIES FAILED VERIFICATION"
        echo ""
        echo "? Libraries are NOT ready for delivery!"
        echo ""
        echo "Common fixes needed:"
        echo "1. Add -fvisibility=default to compilation"
        echo "2. Use __attribute__((visibility(\"default\"))) in source"
        echo "3. Ensure functions are not declared as static"
        echo "4. Verify ARM64 cross-compilation settings"
        echo ""
        echo "Please fix the issues and run this script again."
        return 1
    fi
}

# Run main function
main "$@"
