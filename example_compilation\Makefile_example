# Example Makefile for ARM64 Linux shared libraries
# This demonstrates the correct compilation process

CC = gcc
CFLAGS = -fPIC -fvisibility=default -Wall -O2 -march=armv8-a
LDFLAGS = -shared

# For cross-compilation (if building on non-ARM64 system)
# CC = aarch64-linux-gnu-gcc

# Target verification
ARCH := $(shell uname -m)
ifneq ($(ARCH),aarch64)
    $(warning Warning: Not building on ARM64. Using cross-compilation.)
    CC = aarch64-linux-gnu-gcc
endif

# Library targets
LIBRARIES = libG2_encryption.so libG1_alg1.so libG1_alg3.so

all: $(LIBRARIES) verify

# G2 Encryption Library
libG2_encryption.so: G2_encryption_example.o
	$(CC) $(LDFLAGS) -o $@ $^
	@echo "Built: $@"

G2_encryption_example.o: G2_encryption_example.c
	$(CC) $(CFLAGS) -c $< -o $@

# G1 Algorithm Library 1 (example)
libG1_alg1.so: G1_alg1_example.o
	$(CC) $(LDFLAGS) -o $@ $^
	@echo "Built: $@"

G1_alg1_example.o: G1_alg1_example.c
	$(CC) $(CFLAGS) -c $< -o $@

# G1 Algorithm Library 3 (example)
libG1_alg3.so: G1_alg3_example.o
	$(CC) $(LDFLAGS) -o $@ $^
	@echo "Built: $@"

G1_alg3_example.o: G1_alg3_example.c
	$(CC) $(CFLAGS) -c $< -o $@

# Create example source files if they don't exist
G1_alg1_example.c:
	@echo "Creating example G1_alg1_example.c..."
	@echo '__attribute__((visibility("default"))) void Trans_InputCode(unsigned char* input, unsigned char* output);' > $@
	@echo 'void Trans_InputCode(unsigned char* input, unsigned char* output) {' >> $@
	@echo '    for(int i = 0; i < 3; i++) output[i] = input[i] ^ 0xAA;' >> $@
	@echo '}' >> $@

G1_alg3_example.c:
	@echo "Creating example G1_alg3_example.c..."
	@echo '__attribute__((visibility("default"))) unsigned short CalcInCode(unsigned char* input, unsigned char* key);' > $@
	@echo 'unsigned short CalcInCode(unsigned char* input, unsigned char* key) {' >> $@
	@echo '    return (input[0] ^ key[0]) | ((input[1] ^ key[1]) << 8);' >> $@
	@echo '}' >> $@

# Verification target
verify: $(LIBRARIES)
	@echo ""
	@echo "=========================================="
	@echo "VERIFICATION RESULTS"
	@echo "=========================================="
	@for lib in $(LIBRARIES); do \
		if [ -f $$lib ]; then \
			echo "Checking $$lib:"; \
			echo "  File type: $$(file $$lib)"; \
			echo "  Exported symbols:"; \
			nm -D $$lib | grep -E " T " || echo "    No exported functions found!"; \
			echo ""; \
		else \
			echo "$$lib: NOT FOUND"; \
		fi; \
	done
	@echo "Run './vendor_verification_script.sh' for complete verification"

# Test target - creates and runs a simple test
test: $(LIBRARIES)
	@echo "Creating runtime test..."
	@echo '#include <stdio.h>' > test_runtime.c
	@echo '#include <dlfcn.h>' >> test_runtime.c
	@echo 'int main() {' >> test_runtime.c
	@echo '    void* handle = dlopen("./libG2_encryption.so", RTLD_LAZY);' >> test_runtime.c
	@echo '    if (!handle) { printf("Failed to load library\\n"); return 1; }' >> test_runtime.c
	@echo '    void* func = dlsym(handle, "G2_VinToSc");' >> test_runtime.c
	@echo '    if (!func) { printf("Function not found\\n"); return 1; }' >> test_runtime.c
	@echo '    printf("Success: Library and function loaded\\n");' >> test_runtime.c
	@echo '    dlclose(handle); return 0;' >> test_runtime.c
	@echo '}' >> test_runtime.c
	@$(CC) -o test_runtime test_runtime.c -ldl
	@echo "Running test..."
	@./test_runtime
	@rm -f test_runtime test_runtime.c

# Clean target
clean:
	rm -f *.o $(LIBRARIES) test_runtime test_runtime.c
	rm -f G1_alg1_example.c G1_alg3_example.c

# Help target
help:
	@echo "Available targets:"
	@echo "  all     - Build all libraries"
	@echo "  verify  - Verify built libraries"
	@echo "  test    - Run runtime loading test"
	@echo "  clean   - Clean build files"
	@echo "  help    - Show this help"
	@echo ""
	@echo "Key compilation flags used:"
	@echo "  -fPIC                 : Position Independent Code"
	@echo "  -fvisibility=default  : Export all symbols by default"
	@echo "  -shared               : Create shared library"
	@echo "  -march=armv8-a        : Target ARM64 architecture"

.PHONY: all verify test clean help
