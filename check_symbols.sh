#!/bin/bash

echo "=== Checking JNI Library Symbols ==="
echo "Symbols in libJniAlgorithm.so:"
nm -D bin/libJniAlgorithm.so | grep Java || echo "No Java symbols found"

echo ""
echo "=== Checking G2 Library Symbols ==="
echo "Symbols in libG2Encryption.so:"
nm -D lib/libG2Encryption.so | grep G2 || echo "No G2 symbols found"

echo ""
echo "=== Checking Library Dependencies ==="
echo "Dependencies of libJniAlgorithm.so:"
ldd bin/libJniAlgorithm.so || echo "ldd not available"

echo ""
echo "Dependencies of libG2Encryption.so:"
ldd lib/libG2Encryption.so || echo "ldd not available"

echo ""
echo "=== Checking File Types ==="
echo "libJniAlgorithm.so:"
file bin/libJniAlgorithm.so

echo "libG2Encryption.so:"
file lib/libG2Encryption.so

echo ""
echo "=== Testing G2 Library Loading ==="
echo "Attempting to load G2 library with dlopen..."

cat > test_dlopen.c << 'EOF'
#include <stdio.h>
#include <dlfcn.h>

int main() {
    void* handle = dlopen("./lib/libG2Encryption.so", RTLD_LAZY);
    if (!handle) {
        printf("Error loading libG2Encryption.so: %s\n", dlerror());
        return 1;
    }
    
    printf("Successfully loaded libG2Encryption.so\n");
    
    void* vinToSc = dlsym(handle, "G2_VinToSc");
    if (!vinToSc) {
        printf("Error finding G2_VinToSc: %s\n", dlerror());
    } else {
        printf("Successfully found G2_VinToSc\n");
    }
    
    void* calcInCode = dlsym(handle, "G2_CalcInCode");
    if (!calcInCode) {
        printf("Error finding G2_CalcInCode: %s\n", dlerror());
    } else {
        printf("Successfully found G2_CalcInCode\n");
    }
    
    dlclose(handle);
    return 0;
}
EOF

gcc -o test_dlopen test_dlopen.c -ldl && ./test_dlopen
rm -f test_dlopen test_dlopen.c
