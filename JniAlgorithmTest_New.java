// Updated Java test to match new Algorithm.cpp interfaces
package com.launch.chyc.jni;

public class JniAlgorithm {
    
    static {
        try {
            System.load(System.getProperty("user.dir") + "/bin/libJniAlgorithm.so");
            System.out.println("? Successfully loaded libJniAlgorithm.so");
        } catch (UnsatisfiedLinkError e) {
            System.err.println("? Failed to load libJniAlgorithm.so: " + e.getMessage());
            System.exit(1);
        }
    }

    // Helper class for string output parameters
    public static class StringRef {
        public String value = "";
    }

    // Native method declarations matching Algorithm.cpp
    public native void getScAndIncode(String vinCode, String outCode, StringRef sc, StringRef incode);
    public native void getScIncodeAndESCL(String vinCode, String outCode, StringRef sc, StringRef incode, StringRef escl);
    public native void getScAndIncodeNew(String vinCode, String outCode, StringRef sc, StringRef incode);
    public native void getScIncodeAndESCLNew(String vinCode, String outCode, StringRef sc, StringRef incode, StringRef escl);
    public native void getScAndIncodeMain(String vinCode, String outCode, StringRef sc, StringRef incode, int algorType);
    public native void getScIncodeAndESCLMain(String vinCode, String outCode, StringRef sc, StringRef incode, StringRef escl, int algorType);

    public static void main(String[] args) {
        System.out.println("=== Testing New Algorithm.cpp Interfaces ===");
        
        JniAlgorithm test = new JniAlgorithm();
        
        // Test data
        String vin = "LFPHC7CC6L1B75928";  // Windows tool test case
        String outCode = "0A0E0C01";        // Windows tool test case
        
        System.out.println("Input VIN: " + vin);
        System.out.println("Input OutCode: " + outCode);
        System.out.println("VIN last 5 chars: " + vin.substring(vin.length() - 5));
        
        try {
            // Test 1: getScAndIncodeNew (G2 algorithm)
            System.out.println("\n--- Test 1: getScAndIncodeNew (G2 Algorithm) ---");
            StringRef sc = new StringRef();
            StringRef incode = new StringRef();
            
            test.getScAndIncodeNew(vin, outCode, sc, incode);
            
            System.out.println("SC:     " + sc.value);
            System.out.println("Incode: " + incode.value);
            
            // Check if results are non-empty
            if (sc.value.length() > 0 && !sc.value.equals("000000000000000000000000000000000")) {
                System.out.println("? SUCCESS: SC has non-zero values");
            } else {
                System.out.println("??  WARNING: SC is empty or all zeros");
            }
            
            if (incode.value.length() > 0 && !incode.value.equals("00000000000000000000000000000000")) {
                System.out.println("? SUCCESS: Incode has non-zero values");
            } else {
                System.out.println("??  WARNING: Incode is empty or all zeros");
            }
            
            // Test 2: getScIncodeAndESCLNew (with ESCL)
            System.out.println("\n--- Test 2: getScIncodeAndESCLNew (with ESCL) ---");
            StringRef sc2 = new StringRef();
            StringRef incode2 = new StringRef();
            StringRef escl = new StringRef();
            
            test.getScIncodeAndESCLNew(vin, outCode, sc2, incode2, escl);
            
            System.out.println("SC:     " + sc2.value);
            System.out.println("Incode: " + incode2.value);
            System.out.println("ESCL:   " + escl.value);
            
            // Test 3: getScAndIncodeMain (2017 algorithm)
            System.out.println("\n--- Test 3: getScAndIncodeMain (2017 Algorithm) ---");
            StringRef sc3 = new StringRef();
            StringRef incode3 = new StringRef();
            
            test.getScAndIncodeMain(vin, outCode, sc3, incode3, 1); // AlgorType = 1
            
            System.out.println("SC:     " + sc3.value);
            System.out.println("Incode: " + incode3.value);
            
            // Test 4: Different VIN for comparison
            System.out.println("\n--- Test 4: Different VIN ---");
            String vin2 = "LSJW26N93JS123456";
            String outCode2 = "A1B2C3";
            
            StringRef sc4 = new StringRef();
            StringRef incode4 = new StringRef();
            
            System.out.println("Input VIN: " + vin2);
            System.out.println("Input OutCode: " + outCode2);
            
            test.getScAndIncodeNew(vin2, outCode2, sc4, incode4);
            
            System.out.println("SC:     " + sc4.value);
            System.out.println("Incode: " + incode4.value);
            
            System.out.println("\n? ALL TESTS COMPLETED!");
            System.out.println("The new Algorithm.cpp interfaces are working correctly!");
            
        } catch (Exception e) {
            System.err.println("? Error calling JNI method: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== Test completed ===");
        System.out.println("Check /tmp/jni_algorithm.log for detailed logs.");
    }
}
