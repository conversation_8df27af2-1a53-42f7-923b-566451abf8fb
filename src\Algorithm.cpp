#include <jni.h>
#include <string.h>
#include <stdio.h>
#include <dlfcn.h>  // Linux dynamic library loading
#include <cstdlib>  // for malloc
#include <cstring>  // for memcpy, memset, strcat
#include <cstddef>  // for size_t
#include "../include/com_launch_chyc_jni_JniAlgorithm.h"

// Additional includes
#include <fstream>
#include <ctime>
#include <string>
#include <sstream>
#include <iomanip>  // for std::setfill, std::setw

// Linux equivalents for Windows API
#define HMODULE void*
#define LoadLibrary(name) dlopen(name, RTLD_LAZY)
#define GetProcAddress(handle, name) dlsym(handle, name)
#define FreeLibrary(handle) dlclose(handle)

// Global log file
std::ofstream jniLogFile;
JNIEXPORT jint JNICALL
JNI_OnLoad_JniAlgorithm(JavaVM* vm, void* reserved)
{
    return JNI_OnLoad(vm, reserved);   // ????????????
}
// Function to initialize logging
void jniInitLogging() {
    if (!jniLogFile.is_open()) {
        jniLogFile.open("/tmp/jni_algorithm.log", std::ios::app);

        // Add timestamp
        time_t now = time(0);
        char* dt = ctime(&now);
        jniLogFile << "\n[" << dt << "] libJniAlgorithm.so loaded" << std::endl;
    }
}

// Function to log messages
void jniLogMessage(const std::string& message) {
    if (jniLogFile.is_open()) {
        jniLogFile << message << std::endl;
    }
}

// Function to close logging
void jniCloseLogging() {
    if (jniLogFile.is_open()) {
        jniLogFile << "libJniAlgorithm.so unloaded" << std::endl;
        jniLogFile.close();
    }
}

// Add this to your JNI_OnLoad function (if you have one)
JNIEXPORT jint JNICALL JNI_OnLoad(JavaVM* vm, void* reserved) {
    jniInitLogging();
    jniLogMessage("JNI_OnLoad called");
    return JNI_VERSION_1_6;
}

// Add this to your JNI_OnUnload function (if you have one)
JNIEXPORT void JNICALL JNI_OnUnload(JavaVM* vm, void* reserved) {
    jniLogMessage("JNI_OnUnload called");
    jniCloseLogging();
}


JavaVM *jvm;
JNIEnv *static_env;

#ifdef __cplusplus
extern "C" {
#endif
	//jstring to char
	char* jstringToChar(JNIEnv* env, jstring jstr)
	{
		char* rtn = NULL;
		jclass strClass = env->FindClass("java/lang/String");
		jstring strencode = env->NewStringUTF("utf-8");
		jmethodID mid = env->GetMethodID(strClass, "getBytes","(Ljava/lang/String;)[B");
		jbyteArray barr = (jbyteArray)(env)->CallObjectMethod(jstr, mid,strencode);
		jsize alen = env->GetArrayLength(barr);
		jbyte* ba = env->GetByteArrayElements(barr, JNI_FALSE);
		if (alen > 0) {
			rtn = (char*) malloc(alen + 1);
			memcpy(rtn, ba, alen);
			rtn[alen] = '\0';
		}
		env->ReleaseByteArrayElements(barr, ba, 0);
		env->DeleteLocalRef(strClass);
		env->DeleteLocalRef(strencode);
		env->DeleteLocalRef(barr);
		return rtn;
	}

	//char* to jstring
	jstring stoJstring( JNIEnv* env, const char* pat )
	{
		jstring result;
		jclass strClass = env->FindClass("java/lang/String");
		jmethodID ctorID = env->GetMethodID(strClass, "<init>", "([BLjava/lang/String;)V");
		jbyteArray bytes = env->NewByteArray(strlen(pat));
		env->SetByteArrayRegion(bytes, 0, strlen(pat), (jbyte*)pat);
		jstring encoding = env->NewStringUTF("utf-8");
		result = (jstring)(env)->NewObject(strClass, ctorID, bytes, encoding);

		(env)->DeleteLocalRef(strClass);
		(env)->DeleteLocalRef(bytes);
		(env)->DeleteLocalRef(encoding);
		return result;
	}

    int TranslateChar(unsigned char v)
    {
        int i = 0;
        if (v >= '0' && v <= '9')
        {
            i = v - '0';
        }
        if (v >= 'A' && v < 'a')
        {
            i =  v - 'A' + 10;
        }
        if (v >= 'a')
        {
            i =  v - 'a' + 10;
        }
        return i;
    }

    //???sc???????vin????5?????????
	//???????
    unsigned char* GetSc(unsigned char* vinCode)
    {
        unsigned char v[6] = {0};
        for (int i = 12; i < 17; i++) //???vin????5??
        {
            v[i - 12] = vinCode[i];
        }
        unsigned char* outputcode = new unsigned char[10];
        memset(outputcode, 0, sizeof(unsigned char) * 10);
        typedef void (*alg1)(unsigned char*, unsigned char*);
        HMODULE hmodule = LoadLibrary("./lib/libG1_alg1.so");
        if (NULL == hmodule)
        {
            return outputcode;
        }
        alg1 fun = (alg1)GetProcAddress(hmodule, "Trans_InputCode");
        if (NULL != fun)
        {
            fun(v, outputcode);
        }
        FreeLibrary(hmodule);
        return outputcode;
    }

    //???incode??
    unsigned short GetIncode(unsigned char* outCode, unsigned char* sc)
    {
        typedef unsigned short (*CalcInCode)(unsigned char*, unsigned char*);
        unsigned short output = 0;
        HMODULE hmodule = LoadLibrary("./lib/libG1_alg3.so");
        if (NULL == hmodule)
        {
            return output;
        }
        CalcInCode fun = (CalcInCode)GetProcAddress(hmodule, "CalcInCode");
        unsigned char translateOutCode[3] = {0};
        for (int i = 0; i < 3; i++)
        {
            translateOutCode[i] = TranslateChar(outCode[i * 2]) * 16 + TranslateChar(outCode[i * 2 + 1]);
        }
        if (NULL != fun)
        {
            output = fun(translateOutCode, sc);
        }
        return output;
    }



    //???escl??
    unsigned long GetESCL(unsigned char* vinCode)
    {
        unsigned char v[18] = {0};
        memcpy(v, vinCode, 17);
        unsigned short int ch = 0x3333; //???????????3333
        unsigned long key1 = 0x43333033; //?????????????43333033
        unsigned long escl = 0;
        typedef unsigned long (*alg)(unsigned char*, unsigned short int, unsigned long);
        HMODULE hmodule = LoadLibrary("./lib/libC303.so"); // Load dynamic library
        if (NULL == hmodule)
        {
            return escl;
        }
        alg fun = (alg)GetProcAddress(hmodule, "alg"); //??????????
        if (NULL != fun)
        {
            escl = fun(v, ch, key1);
        }
        FreeLibrary(hmodule);
        return escl;
    }

 //   __declspec(dllexport) void _Java_com_launch_yqyc_jni_JniAlgorithm_getScAndIncode(char* vinCode, char* outCode, char* sc, char* incode)
	JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScAndIncode(JNIEnv * env, jobject job, jstring vinCode, jstring outCode, jobject sc, jobject incode)
    {
		/*
        unsigned char* scTemp = GetSc((unsigned char*)vinCode);
        unsigned short incodeTemp = GetIncode((unsigned char*)outCode, scTemp);
        sprintf(sc, "%02X%02X%02X", scTemp[0], scTemp[1], scTemp[2]);
        sprintf(incode, "%04X", incodeTemp);
        free(scTemp);
		*/
		char temp1[10] = {0};
		int i = 0;
		char *Tvincode = NULL;
		char *ToutCode = NULL;
		char Tsc[60] = {0};
		char Tincode[60] = {0};




		Tvincode = jstringToChar(env,vinCode);
		ToutCode = jstringToChar(env,outCode);

		unsigned char* scTemp = GetSc((unsigned char*)Tvincode);
		unsigned short incodeTemp = GetIncode((unsigned char*)ToutCode, scTemp);

		for(i=0;i<3;i++)
		{
			memset(temp1,0,sizeof(temp1));
			sprintf(temp1, "%02X", scTemp[i]);
			strcat(Tsc,temp1);
		}

//		for(i=0;i<16;i++)
//		{
//			memset(temp1,0,sizeof(temp1));
//			sprintf(temp1, "%02X", incodeTemp[i]);
//			strcat(Tincode,temp1);
//		}

		sprintf(Tincode,"%04X",incodeTemp);



		jstring scStr = stoJstring(env,Tsc);
		jclass classSC = env->GetObjectClass(sc);
		jfieldID scValue = env->GetFieldID(classSC,"value","Ljava/lang/String;");
		env->SetObjectField(sc ,scValue,scStr);

		jstring incodeStr = stoJstring(env,Tincode);
		jclass classIncode = env->GetObjectClass(incode);
		jfieldID incodeValue = env->GetFieldID(classIncode,"value","Ljava/lang/String;");
		env->SetObjectField(incode ,incodeValue,incodeStr);


      //  free(scTemp);
		delete []scTemp;
	//	free(incodeTemp);




    }

//    __declspec(dllexport) void _Java_com_launch_yqyc_jni_JniAlgorithm_getScIncodeAndESCL(char* vinCode, char* outCode, char* sc, char* incode, char* escl)
	JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScIncodeAndESCL(JNIEnv * env, jobject job, jstring vinCode, jstring outCode, jobject sc, jobject incode, jobject escl)
    {
		/*
        unsigned char* scTemp = GetSc((unsigned char*)vinCode);
        unsigned short incodeTemp = GetIncode((unsigned char*)outCode, scTemp);
        unsigned long esclTemp = GetESCL((unsigned char*)vinCode);
        sprintf(sc, "%02X%02X%02X", scTemp[0], scTemp[1], scTemp[2]);
        sprintf(incode, "%04X", incodeTemp);
        sprintf(escl, "%08X", esclTemp);
        free(scTemp);
		*/

		char temp1[10] = {0};
		int i = 0;

		char *Tvincode = NULL;
		char *ToutCode = NULL;
		char Tsc[60] = {0};
		char Tincode[60] = {0};
		char TempEscl[20] = {0};


		Tvincode = jstringToChar(env,vinCode);
		ToutCode = jstringToChar(env,outCode);

        unsigned char* scTemp = GetSc((unsigned char*)Tvincode);
		// unsigned short incodeTemp = GetIncode((unsigned char*)outCode, scTemp);

		//		memcpy(scTemp,"\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F\x10",16);
		//		memcpy(outCode,"\x24\x7D\xFD\x91",4);

		unsigned short incodeTemp = GetIncode((unsigned char*)ToutCode, scTemp);
        unsigned long esclTemp = GetESCL((unsigned char*)Tvincode);

		for(i=0;i<3;i++)
		{
			memset(temp1,0,sizeof(temp1));
			sprintf(temp1, "%02X", scTemp[i]);
			strcat(Tsc,temp1);
		}

		//		for(i=0;i<16;i++)
		//		{
		//			memset(temp1,0,sizeof(temp1));
		//			sprintf(temp1, "%02X", incodeTemp[i]);
		//			strcat(Tincode,temp1);
		//		}

		sprintf(Tincode,"%04X",incodeTemp);


		//sc
		jstring scStr = stoJstring(env,Tsc);
		jclass classSC = env->GetObjectClass(sc);
		jfieldID scValue = env->GetFieldID(classSC,"value","Ljava/lang/String;");
		env->SetObjectField(sc ,scValue,scStr);

		//incode
		jstring incodeStr = stoJstring(env,Tincode);
		jclass classIncode = env->GetObjectClass(incode);
		jfieldID incodeValue = env->GetFieldID(classIncode,"value","Ljava/lang/String;");
		env->SetObjectField(incode ,incodeValue,incodeStr);

		//sc
		sprintf(TempEscl, "%08lX", esclTemp);
		jstring esclStr = stoJstring(env,TempEscl);
		jclass classEscl = env->GetObjectClass(escl);
		jfieldID esclValue = env->GetFieldID(classEscl,"value","Ljava/lang/String;");
		env->SetObjectField(escl ,esclValue,esclStr);


		//        sprintf(sc, "%02X%02X%02X", scTemp[0], scTemp[1], scTemp[2]);
		//        sprintf(incode, "%04X", incodeTemp);
		//        sprintf(escl, "%08X", esclTemp);
       // free(scTemp);
		delete []scTemp;
	//	free(incodeTemp);
    }


//???????sc????16??????outcode????4??????incode????16??????vin?????sc??sc???outcode?????incode??
   //???sc???????vin????5?????????
	//2016???????????
    unsigned char* GetSc_New(unsigned char* vinCode)
    {
        unsigned char v[6] = {0};
        for (int i = 12; i < 17; i++) //???vin????5??
        {
            v[i - 12] = vinCode[i];
        }

		unsigned char* outputcode = new unsigned char[18];
        memset(outputcode, 0, sizeof(unsigned char) * 18);
        typedef void (*alg1)(unsigned char*, unsigned char*);

		HMODULE hmodule = LoadLibrary("./lib/libG2_encryption.so");

        if (NULL == hmodule)
        {
            return outputcode;
        }

		alg1 fun = (alg1)GetProcAddress(hmodule, "G2_VinToSc");
        if (NULL != fun)
        {
            fun(v, outputcode);
        }
        FreeLibrary(hmodule);
        return outputcode;
    }

	//2016???????????
    //???incode??

	unsigned char* GetIncode_New(unsigned char* outCode, unsigned char* sc)
    {

		typedef void (*CalcInCode)(unsigned char*, unsigned char*, unsigned char*);

		unsigned char* output = new unsigned char[18];
		memset(output, 0, sizeof(unsigned char) * 18);

		HMODULE hmodule = LoadLibrary("./lib/libG2_encryption.so");
        if (NULL == hmodule)
        {
            return output;
        }

		CalcInCode fun = (CalcInCode)GetProcAddress(hmodule, "G2_CalcInCode");

       unsigned char translateOutCode[5] = {0};
         for (int i = 0; i < 4; i++)
         {
              translateOutCode[i] = TranslateChar(outCode[i * 2]) * 16 + TranslateChar(outCode[i * 2 + 1]);
         }

        if (NULL != fun)
        {

            fun(translateOutCode, sc,output);

        }
        return output;
    }


	//2017???????????
	/**********************************************************
****  Function:     EncryptAlg1
****  Author:       ?????
****  Data:         2011-06-24
****  Version:      1.0
****  Description:  ???????6???????????????????????????4???????
****  Input:        ming    6???????????????????????????      // ???????
****  Output:       out     4??????????????????????????????      // ????????
****  Return:       void    ??????                            // ??????????
****  History:      Data        Author    Description
****                2011-06-24  ?????    ???????
****
************************************************************/
//void EncryptAlg1(unsigned char ming[6], unsigned char out[4]);
	unsigned char* GetSc_2017(unsigned char* vinCode)
    {
        unsigned char v[7] = {0};
        for (int i = 11; i < 17; i++) //???vin????6??
        {
            v[i - 11] = vinCode[i];
        }
    //    unsigned char* outputcode = new unsigned char[10];
		unsigned char* outputcode = new unsigned char[18];
        memset(outputcode, 0, sizeof(unsigned char) * 18);
        typedef void (*alg1)(unsigned char*, unsigned char*);
      //  HMODULE hmodule = LoadLibrary("alg1.dll");
		HMODULE hmodule = LoadLibrary("./lib/libencryptalg1.so");

        if (NULL == hmodule)
        {
            return outputcode;
        }
      //  alg1 fun = (alg1)GetProcAddress(hmodule, "Trans_InputCode");
	//	alg1 fun = (alg1)GetProcAddress(hmodule, "G2Trans_InputCode");
		alg1 fun = (alg1)GetProcAddress(hmodule, "EncryptAlg1");
        if (NULL != fun)
        {
            fun(v, outputcode);
        }
        FreeLibrary(hmodule);
        return outputcode;
    }


	/**********************************************************
****  Function:     EncryptAlg2
****  Author:       ?????
****  Data:         2011-06-24
****  Version:      1.0
****  Description:  ??4??????????????????4????????????????????????? ???4?????????????????
****  Input:        ming    4?????????????????????      // ???????
****                key     4????????????????????
****  Output:       out     4?????????????????????      // ????????
****  Return:       void    ??????        // ??????????
****  History:      Data        Author    Description
****                2011-06-24  ?????    ???????
************************************************************/
//void EncryptAlg2(unsigned char ming[4], unsigned char key[4], unsigned char out[4]);
	//2017???????????
    //???incode??
  //  unsigned short GetIncode(unsigned char* outCode, unsigned char* sc)
	unsigned char* GetIncode_2017(unsigned char* outCode, unsigned char* sc)
    {
      //  typedef unsigned short (*CalcInCode)(unsigned char*, unsigned char*);
		typedef void (*CalcInCode)(unsigned char*, unsigned char*, unsigned char*);

		unsigned char* output = new unsigned char[18];
		memset(output, 0, sizeof(unsigned char) * 18);
     //   unsigned short output = 0;
     //   HMODULE hmodule = LoadLibrary("alg3.dll");
		HMODULE hmodule = LoadLibrary("./lib/libencryptalg2.so");
        if (NULL == hmodule)
        {
            return output;
        }
    //    CalcInCode fun = (CalcInCode)GetProcAddress(hmodule, "CalcInCode");
	//	CalcInCode fun = (CalcInCode)GetProcAddress(hmodule, "G2CalcInCode");
		CalcInCode fun = (CalcInCode)GetProcAddress(hmodule, "EncryptAlg2");

       unsigned char translateOutCode[5] = {0};
         for (int i = 0; i < 4; i++)
         {
              translateOutCode[i] = TranslateChar(outCode[i * 2]) * 16 + TranslateChar(outCode[i * 2 + 1]);
         }

        if (NULL != fun)
        {
		//	memcpy(translateOutCode,"\x11\x22\x33\x44",4);
		//	memcpy(sc,"\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F\x10",16);
		//	memcpy(translateOutCode,"\x24\x7D\xFD\x91",4);
        //    fun(sc, translateOutCode,output);//????????????SC,OUTCODE,INCODE
			fun(translateOutCode,sc ,output);//????????????OUTCODE,SC,INCODE
		//	fun(outCode, sc,output);
        }
        return output;
    }





  //  __declspec(dllexport) void _Java_com_launch_yqyc_jni_JniAlgorithm_getScAndIncode_New(char* vinCode, unsigned char* outCode, char* sc, char* incode)
//	 __declspec(dllexport) void GetScAndIncode(char* vinCode, unsigned char* outCode, char* sc, char* incode)
	//jint AlgorType???????????????????????????????????????????????????
	JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScAndIncodeNew(JNIEnv * env, jobject job, jstring vinCode, jstring outCode, jobject sc, jobject incode)
    {

		char temp1[10] = {0};
		int i = 0;
//		char Tvincode[20] = {0};
//		char ToutCode[20] = {0};
//		char Tsc[20] = {0};
//		char Tincode[20] = {0};
		char *Tvincode = NULL;
		char *ToutCode = NULL;
		char Tsc[60] = {0};
		char Tincode[60] = {0};
		unsigned char* scTemp = NULL;
		unsigned char* incodeTemp = NULL;

		/*
		FILE * fp = NULL;

				fp=fopen("SaveData.txt","a");*/


		Tvincode = jstringToChar(env,vinCode);
		ToutCode = jstringToChar(env,outCode);


			// unsigned char* scTemp = GetSc_New((unsigned char*)vinCode);
			scTemp = GetSc_New((unsigned char*)Tvincode);//scTemp??16?????
			// unsigned short incodeTemp = GetIncode((unsigned char*)outCode, scTemp);
			//	unsigned char* incodeTemp = GetIncode_New(outCode, scTemp);



			incodeTemp = GetIncode_New((unsigned char*)ToutCode, scTemp);
			for(i=0;i<16;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", scTemp[i]);
				//	strcat(sc,temp1);
				strcat(Tsc,temp1);
			}

			for(i=0;i<16;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", incodeTemp[i]);
				//	strcat(incode,temp1);
				strcat(Tincode,temp1);
			}

			//fprintf(fp,"Tsc:%s",Tsc);

			jstring scStr = stoJstring(env,Tsc);
			jclass classSC = env->GetObjectClass(sc);
			jfieldID scValue = env->GetFieldID(classSC,"value","Ljava/lang/String;");
			env->SetObjectField(sc ,scValue,scStr);

			jstring incodeStr = stoJstring(env,Tincode);
			jclass classIncode = env->GetObjectClass(incode);
			jfieldID incodeValue = env->GetFieldID(classIncode,"value","Ljava/lang/String;");
			env->SetObjectField(incode ,incodeValue,incodeStr);



			//		sc = stoJstring(env,Tsc);
			//		incode = stoJstring(env,Tincode);

			//  sprintf(sc, "%02X%02X%02X", scTemp[0], scTemp[1], scTemp[2]);
			// sprintf(incode, "%04X", incodeTemp);


      //  free(scTemp);
	//	free(incodeTemp);

		delete []scTemp;
		delete []incodeTemp;

	//	fclose(fp);
    }

//    __declspec(dllexport) void _Java_com_launch_yqyc_jni_JniAlgorithm_getScIncodeAndESCL_New(char* vinCode, unsigned char* outCode, char* sc, char* incode, char* escl)
	//jint AlgorType???????????????????????????????????????????????????
	JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScIncodeAndESCLNew(JNIEnv * env, jobject job, jstring vinCode, jstring outCode, jobject sc, jobject incode, jobject escl)
//	 __declspec(dllexport) void GetScIncodeAndESCL(char* vinCode, unsigned char* outCode, char* sc, char* incode, char* escl)
	 {
		char temp1[10] = {0};
		int i = 0;

		char *Tvincode = NULL;
		char *ToutCode = NULL;
		char Tsc[60] = {0};
		char Tincode[60] = {0};
		char TempEscl[20] = {0};
		unsigned char* scTemp=NULL;
		unsigned char* incodeTemp=NULL;
		unsigned long esclTemp=0;


		Tvincode = jstringToChar(env,vinCode);
		ToutCode = jstringToChar(env,outCode);



			scTemp = GetSc_New((unsigned char*)Tvincode);
			// unsigned short incodeTemp = GetIncode((unsigned char*)outCode, scTemp);

			//		memcpy(scTemp,"\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F\x10",16);
			//		memcpy(outCode,"\x24\x7D\xFD\x91",4);

			incodeTemp = GetIncode_New((unsigned char*)ToutCode, scTemp);
			esclTemp = GetESCL((unsigned char*)Tvincode);

			for(i=0;i<16;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", scTemp[i]);
				strcat(Tsc,temp1);
			}
			for(i=0;i<16;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", incodeTemp[i]);
				strcat(Tincode,temp1);
			}

			//sc
			jstring scStr = stoJstring(env,Tsc);
			jclass classSC = env->GetObjectClass(sc);
			jfieldID scValue = env->GetFieldID(classSC,"value","Ljava/lang/String;");
			env->SetObjectField(sc ,scValue,scStr);

			//incode
			jstring incodeStr = stoJstring(env,Tincode);
			jclass classIncode = env->GetObjectClass(incode);
			jfieldID incodeValue = env->GetFieldID(classIncode,"value","Ljava/lang/String;");
			env->SetObjectField(incode ,incodeValue,incodeStr);

			//sc
			sprintf(TempEscl, "%08lX", esclTemp);
			jstring esclStr = stoJstring(env,TempEscl);
			jclass classEscl = env->GetObjectClass(escl);
			jfieldID esclValue = env->GetFieldID(classEscl,"value","Ljava/lang/String;");
			env->SetObjectField(escl ,esclValue,esclStr);


			//        sprintf(sc, "%02X%02X%02X", scTemp[0], scTemp[1], scTemp[2]);
			//        sprintf(incode, "%04X", incodeTemp);
			//        sprintf(escl, "%08X", esclTemp);





      //  free(scTemp);
	//	free(incodeTemp);

		delete []scTemp;
		delete []incodeTemp;
    }


///////// ?????????2017???????????????????????????????jint AlgorType????????????????????????????????????????????????????????????????????????????????????????????
 //  __declspec(dllexport) void _Java_com_launch_yqyc_jni_JniAlgorithm_getScAndIncode_New(char* vinCode, unsigned char* outCode, char* sc, char* incode)
//	 __declspec(dllexport) void GetScAndIncode(char* vinCode, unsigned char* outCode, char* sc, char* incode)
	//jint AlgorType???????????????????????????????????????????????????
	JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScAndIncodeMain(JNIEnv * env, jobject job, jstring vinCode, jstring outCode, jobject sc, jobject incode,jint AlgorType)
    {

		char temp1[10] = {0};
		int i = 0;
//		char Tvincode[20] = {0};
//		char ToutCode[20] = {0};
//		char Tsc[20] = {0};
//		char Tincode[20] = {0};
		char *Tvincode = NULL;
		char *ToutCode = NULL;
		char Tsc[60] = {0};
		char Tincode[60] = {0};
		unsigned char* scTemp = NULL;
		unsigned char* incodeTemp = NULL;

		/*
		FILE * fp = NULL;

				fp=fopen("SaveData.txt","a");*/


		Tvincode = jstringToChar(env,vinCode);
		ToutCode = jstringToChar(env,outCode);


		if(1==AlgorType)//2017???????X40???????
		{
			// unsigned char* scTemp = GetSc_New((unsigned char*)vinCode);
			scTemp = GetSc_2017((unsigned char*)Tvincode);//scTemp??4?????
			// unsigned short incodeTemp = GetIncode((unsigned char*)outCode, scTemp);
			//	unsigned char* incodeTemp = GetIncode_New(outCode, scTemp);

			incodeTemp = GetIncode_2017((unsigned char*)ToutCode, scTemp);//incodeTemp??4?????
			for(i=0;i<4;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", scTemp[i]);
				//	strcat(sc,temp1);
				strcat(Tsc,temp1);
			}

			for(i=0;i<4;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", incodeTemp[i]);
				//	strcat(incode,temp1);
				strcat(Tincode,temp1);
			}

			//fprintf(fp,"Tsc:%s",Tsc);

			jstring scStr = stoJstring(env,Tsc);
			jclass classSC = env->GetObjectClass(sc);
			jfieldID scValue = env->GetFieldID(classSC,"value","Ljava/lang/String;");
			env->SetObjectField(sc ,scValue,scStr);

			jstring incodeStr = stoJstring(env,Tincode);
			jclass classIncode = env->GetObjectClass(incode);
			jfieldID incodeValue = env->GetFieldID(classIncode,"value","Ljava/lang/String;");
			env->SetObjectField(incode ,incodeValue,incodeStr);

		}

      //  free(scTemp);
	//	free(incodeTemp);

		delete []scTemp;
		delete []incodeTemp;

	//	fclose(fp);
    }

//    __declspec(dllexport) void _Java_com_launch_yqyc_jni_JniAlgorithm_getScIncodeAndESCL_New(char* vinCode, unsigned char* outCode, char* sc, char* incode, char* escl)
	//jint AlgorType???????????????????????????????????????????????????
	JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScIncodeAndESCLMain(JNIEnv * env, jobject job, jstring vinCode, jstring outCode, jobject sc, jobject incode, jobject escl,jint AlgorType)
//	 __declspec(dllexport) void GetScIncodeAndESCL(char* vinCode, unsigned char* outCode, char* sc, char* incode, char* escl)
	 {
		char temp1[10] = {0};
		int i = 0;

		char *Tvincode = NULL;
		char *ToutCode = NULL;
		char Tsc[60] = {0};
		char Tincode[60] = {0};
		char TempEscl[20] = {0};
		unsigned char* scTemp=NULL;
		unsigned char* incodeTemp=NULL;
		unsigned long esclTemp=0;


		Tvincode = jstringToChar(env,vinCode);
		ToutCode = jstringToChar(env,outCode);


		if(1==AlgorType)//2017???????x40???????
		{
			scTemp = GetSc_2017((unsigned char*)Tvincode);
			// unsigned short incodeTemp = GetIncode((unsigned char*)outCode, scTemp);

			//		memcpy(scTemp,"\x01\x02\x03\x04\x05\x06\x07\x08\x09\x0A\x0B\x0C\x0D\x0E\x0F\x10",16);
			//		memcpy(outCode,"\x24\x7D\xFD\x91",4);

			incodeTemp = GetIncode_2017((unsigned char*)ToutCode, scTemp);
			esclTemp = GetESCL((unsigned char*)Tvincode);

			for(i=0;i<4;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", scTemp[i]);
				strcat(Tsc,temp1);
			}
			for(i=0;i<4;i++)
			{
				memset(temp1,0,sizeof(temp1));
				sprintf(temp1, "%02X", incodeTemp[i]);
				strcat(Tincode,temp1);
			}

			//sc
			jstring scStr = stoJstring(env,Tsc);
			jclass classSC = env->GetObjectClass(sc);
			jfieldID scValue = env->GetFieldID(classSC,"value","Ljava/lang/String;");
			env->SetObjectField(sc ,scValue,scStr);

			//incode
			jstring incodeStr = stoJstring(env,Tincode);
			jclass classIncode = env->GetObjectClass(incode);
			jfieldID incodeValue = env->GetFieldID(classIncode,"value","Ljava/lang/String;");
			env->SetObjectField(incode ,incodeValue,incodeStr);

			//sc
			sprintf(TempEscl, "%08lX", esclTemp);
			jstring esclStr = stoJstring(env,TempEscl);
			jclass classEscl = env->GetObjectClass(escl);
			jfieldID esclValue = env->GetFieldID(classEscl,"value","Ljava/lang/String;");
			env->SetObjectField(escl ,esclValue,esclStr);


			//        sprintf(sc, "%02X%02X%02X", scTemp[0], scTemp[1], scTemp[2]);
			//        sprintf(incode, "%04X", incodeTemp);
			//        sprintf(escl, "%08X", esclTemp);
		}


      //  free(scTemp);
	//	free(incodeTemp);

		delete []scTemp;
		delete []incodeTemp;
    }




#ifdef __cplusplus
}
#endif





















