#!/bin/bash

echo "🎯 === 测试正确格式的G2算法 ==="

# 1. 备份并替换算法文件
echo "1. 替换G2算法实现..."
if [ -f src/G2Algorithm.cpp ]; then
    mv src/G2Algorithm.cpp src/G2Algorithm_backup.cpp
fi
mv src/G2Algorithm_Correct.cpp src/G2Algorithm.cpp

# 2. 重新编译
echo "2. 重新编译..."
make clean && make

if [ $? -ne 0 ]; then
    echo "❌ 编译失败!"
    # 恢复备份
    mv src/G2Algorithm.cpp src/G2Algorithm_Correct.cpp
    if [ -f src/G2Algorithm_backup.cpp ]; then
        mv src/G2Algorithm_backup.cpp src/G2Algorithm.cpp
    fi
    exit 1
fi

echo "✅ 编译成功"

# 3. 创建测试程序
echo "3. 创建测试程序..."
cat > test_windows_case.cpp << 'EOF'
#include <iostream>
#include <iomanip>
#include <cstring>

extern "C" {
    void G2_VinToSc_Internal(const unsigned char* vin5, unsigned char* sc16);
    void G2_CalcInCode_Internal(const unsigned char* outcode4, const unsigned char* sc16, unsigned char* incode16);
}

void printHex(const char* label, const unsigned char* data, int len) {
    std::cout << std::setw(12) << std::left << label << ": ";
    for (int i = 0; i < len; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)data[i];
    }
    std::cout << std::dec << std::endl;
}

bool compareArrays(const unsigned char* a, const unsigned char* b, int len) {
    return memcmp(a, b, len) == 0;
}

int main() {
    std::cout << "=== 测试Windows工具用例 ===" << std::endl;
    
    // 测试Windows工具用例: VIN "75928", OutCode "0A0E0C01"
    std::cout << "\n--- Windows工具用例 ---" << std::endl;
    
    unsigned char vin[5] = {0x37, 0x35, 0x39, 0x32, 0x38}; // "75928"
    unsigned char outcode[4] = {0x0A, 0x0E, 0x0C, 0x01};
    unsigned char sc[16] = {0};
    unsigned char incode[16] = {0};
    
    // 期望结果（来自Windows工具截图）
    unsigned char expected_sc[16] = {0xD6, 0x08, 0xCE, 0x5D, 0x5A, 0xC6, 0x3D, 0xF6, 
                                    0x8E, 0xD6, 0xAA, 0x97, 0x66, 0x50, 0xD0, 0x11};
    unsigned char expected_incode[16] = {0x3E, 0x8A, 0x71, 0x7B, 0x43, 0x72, 0x42, 0x15, 
                                        0x0D, 0x89, 0x82, 0x91, 0x6F, 0x80, 0x65, 0xE0}; // 从截图推测
    
    // 执行算法
    G2_VinToSc_Internal(vin, sc);
    G2_CalcInCode_Internal(outcode, sc, incode);
    
    // 显示结果
    std::cout << "VIN (75928): ";
    for (int i = 0; i < 5; i++) {
        std::cout << (char)vin[i];
    }
    std::cout << std::endl;
    
    printHex("OutCode", outcode, 4);
    printHex("实际SC", sc, 16);
    printHex("期望SC", expected_sc, 16);
    printHex("实际InCode", incode, 16);
    
    // 验证结果
    if (compareArrays(sc, expected_sc, 16)) {
        std::cout << "🎉 SC完美匹配Windows工具!" << std::endl;
    } else {
        std::cout << "⚠️  SC与Windows工具不匹配" << std::endl;
    }
    
    // 测试文档中的已知用例
    std::cout << "\n--- 文档测试用例4 ---" << std::endl;
    
    unsigned char doc_vin[5] = {0x31, 0x32, 0x33, 0x34, 0x35}; // "12345"
    unsigned char doc_sc[16] = {0};
    unsigned char doc_expected_sc[16] = {0x39, 0xE2, 0xF3, 0x0F, 0x86, 0xEE, 0x12, 0x07, 
                                        0x68, 0x71, 0xB2, 0x9E, 0xCE, 0xEC, 0xAE, 0x5D};
    
    G2_VinToSc_Internal(doc_vin, doc_sc);
    
    std::cout << "VIN (12345): ";
    for (int i = 0; i < 5; i++) {
        std::cout << (char)doc_vin[i];
    }
    std::cout << std::endl;
    
    printHex("实际SC", doc_sc, 16);
    printHex("期望SC", doc_expected_sc, 16);
    
    if (compareArrays(doc_sc, doc_expected_sc, 16)) {
        std::cout << "✅ 文档测试用例匹配!" << std::endl;
    } else {
        std::cout << "❌ 文档测试用例不匹配!" << std::endl;
    }
    
    return 0;
}
EOF

# 4. 编译并运行测试
echo "4. 编译并运行测试..."
g++ -o test_windows_case test_windows_case.cpp src/G2Algorithm.cpp

if [ $? -ne 0 ]; then
    echo "❌ 测试程序编译失败!"
    exit 1
fi

./test_windows_case

# 5. 运行Java测试
echo ""
echo "5. 运行Java测试..."
mkdir -p com/launch/chyc/jni
cp JniAlgorithmTest.java com/launch/chyc/jni/JniAlgorithm.java
javac com/launch/chyc/jni/JniAlgorithm.java
java com.launch.chyc.jni.JniAlgorithm

# 6. 清理
echo ""
echo "6. 清理..."
rm -f test_windows_case test_windows_case.cpp
rm -rf com/

echo ""
echo "🎯 === 测试完成 ==="
echo "如果Windows工具用例匹配，说明我们的实现是正确的!"
