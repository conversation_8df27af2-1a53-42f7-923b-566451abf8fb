#!/bin/bash

echo "=== Testing New Algorithm.cpp Interface Matching ==="

# 1. First try to compile current version
echo "1. Compiling current version..."
make clean && make

if [ $? -ne 0 ]; then
    echo "Compilation failed! Need to resolve compilation issues"
    exit 1
fi

echo "Compilation successful"

# 2. Create simple Java test class using StringRef approach
echo "2. Creating matching Java test class..."
mkdir -p com/launch/chyc/jni

cat > com/launch/chyc/jni/JniAlgorithm.java << 'EOF'
package com.launch.chyc.jni;

public class JniAlgorithm {
    
    static {
        try {
            System.load(System.getProperty("user.dir") + "/bin/libJniAlgorithm.so");
            System.out.println("Successfully loaded libJniAlgorithm.so");
        } catch (UnsatisfiedLinkError e) {
            System.err.println("Failed to load libJniAlgorithm.so: " + e.getMessage());
            System.exit(1);
        }
    }

    // Helper class for string output parameters
    public static class StringRef {
        public String value = "";
    }

    // Declare a new native method for testing
    public native void getScAndIncodeNew(String vinCode, String outCode, StringRef sc, StringRef incode);

    public static void main(String[] args) {
        System.out.println("=== Testing New Algorithm.cpp ===");
        
        JniAlgorithm test = new JniAlgorithm();
        
        // Test data - Windows tool case
        String vin = "LFPHC7CC6L1B75928";
        String outCode = "0A0E0C01";
        
        System.out.println("Input VIN: " + vin);
        System.out.println("Input OutCode: " + outCode);
        
        try {
            StringRef sc = new StringRef();
            StringRef incode = new StringRef();
            
            System.out.println("\nCalling getScAndIncodeNew...");
            test.getScAndIncodeNew(vin, outCode, sc, incode);
            
            System.out.println("JNI call completed!");
            System.out.println("SC:     " + sc.value);
            System.out.println("Incode: " + incode.value);
            
            // Check results
            if (sc.value.length() > 0 && !sc.value.equals("000000000000000000000000000000000")) {
                System.out.println("SUCCESS: SC has non-zero values");
            } else {
                System.out.println("WARNING: SC is empty or all zeros");
            }
            
        } catch (UnsatisfiedLinkError e) {
            System.err.println("Native method not found: " + e.getMessage());
            System.err.println("This means the Java interface doesn't match Algorithm.cpp");
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("\n=== Test completed ===");
    }
}
EOF

# 3. Generate new header file using javac -h (Java 8+)
echo "3. Generating new JNI header file..."
javac -h . com/launch/chyc/jni/JniAlgorithm.java

if [ -f com_launch_chyc_jni_JniAlgorithm.h ]; then
    echo "New header file generated successfully"
    echo "New header file content:"
    cat com_launch_chyc_jni_JniAlgorithm.h
    
    echo ""
    echo "Do you want to replace the old header file? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        mv com_launch_chyc_jni_JniAlgorithm.h include/
        echo "Header file updated"
        
        # Recompile
        echo "4. Recompiling..."
        make clean && make
        
        if [ $? -eq 0 ]; then
            echo "Recompilation successful"
            
            # Run test
            echo "5. Running test..."
            java com.launch.chyc.jni.JniAlgorithm
        else
            echo "Recompilation failed"
        fi
    else
        echo "Header file not updated, need to manually handle interface matching"
    fi
else
    echo "Header file generation failed"
fi

# Cleanup
rm -rf com/

echo ""
echo "=== Test completed ==="
