/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_launch_chyc_jni_JniAlgorithm */

#ifndef _Included_com_launch_chyc_jni_JniAlgorithm
#define _Included_com_launch_chyc_jni_JniAlgorithm
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_launch_chyc_jni_JniAlgorithm
 * Method:    getScAndIncodeG2
 * Signature: (Ljava/lang/String;Ljava/lang/String;[B[B)V
 */
JNIEXPORT void JNICALL Java_com_launch_chyc_jni_JniAlgorithm_getScAndIncodeG2
  (JNIEnv *, jobject, jstring, jstring, jbyteArray, jbyteArray);

#ifdef __cplusplus
}
#endif
#endif
