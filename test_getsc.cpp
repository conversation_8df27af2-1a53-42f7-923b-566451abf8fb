// Test GetSc function (original algorithm, not GetSc_New)
// This uses libG1_alg1.so which we confirmed is available

#include <iostream>
#include <iomanip>
#include <cstring>

void printHex(const char* label, const unsigned char* data, int len) {
    std::cout << std::setw(12) << std::left << label << ": ";
    for (int i = 0; i < len; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)data[i];
    }
    std::cout << std::dec << std::endl;
}

// Declare the GetSc function from Algorithm.cpp
extern "C" {
    unsigned char* GetSc(unsigned char* vinCode);
}

int main() {
    std::cout << "=== GetSc Function Test ===" << std::endl;
    std::cout << "Testing original GetSc function (uses libG1_alg1.so)" << std::endl;
    
    // Test Case 1: Windows tool case
    std::cout << "\n--- Test Case 1: Windows Tool VIN ---" << std::endl;
    
    std::string vin_str = "LFPHC7CC6L1B75928";
    unsigned char vin[20] = {0};
    strncpy((char*)vin, vin_str.c_str(), sizeof(vin) - 1);
    
    std::cout << "Full VIN: " << vin_str << std::endl;
    std::cout << "VIN last 5 chars: " << vin_str.substr(vin_str.length() - 5) << std::endl;
    
    // Show the VIN bytes that GetSc will use (positions 12-16)
    std::cout << "VIN bytes [12-16]: ";
    for (int i = 12; i < 17; i++) {
        std::cout << (char)vin[i];
    }
    std::cout << " (";
    for (int i = 12; i < 17; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)vin[i];
        if (i < 16) std::cout << " ";
    }
    std::cout << std::dec << ")" << std::endl;
    
    // Call GetSc function
    unsigned char* sc = GetSc(vin);
    if (sc) {
        printHex("Generated SC", sc, 3);  // GetSc returns 3 bytes
        
        // Check if result is non-zero
        bool nonzero = false;
        for (int i = 0; i < 3; i++) {
            if (sc[i] != 0) {
                nonzero = true;
                break;
            }
        }
        
        if (nonzero) {
            std::cout << "? GetSc produced non-zero result!" << std::endl;
        } else {
            std::cout << "??  GetSc returned all zeros" << std::endl;
        }
        
        delete[] sc;
    } else {
        std::cout << "? GetSc returned null" << std::endl;
    }
    
    // Test Case 2: Different VIN
    std::cout << "\n--- Test Case 2: Different VIN ---" << std::endl;
    
    std::string vin2_str = "LSJW26N93JS123456";
    unsigned char vin2[20] = {0};
    strncpy((char*)vin2, vin2_str.c_str(), sizeof(vin2) - 1);
    
    std::cout << "Full VIN: " << vin2_str << std::endl;
    std::cout << "VIN last 5 chars: " << vin2_str.substr(vin2_str.length() - 5) << std::endl;
    
    // Show the VIN bytes that GetSc will use
    std::cout << "VIN bytes [12-16]: ";
    for (int i = 12; i < 17; i++) {
        std::cout << (char)vin2[i];
    }
    std::cout << " (";
    for (int i = 12; i < 17; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)vin2[i];
        if (i < 16) std::cout << " ";
    }
    std::cout << std::dec << ")" << std::endl;
    
    unsigned char* sc2 = GetSc(vin2);
    if (sc2) {
        printHex("Generated SC", sc2, 3);
        
        bool nonzero = false;
        for (int i = 0; i < 3; i++) {
            if (sc2[i] != 0) {
                nonzero = true;
                break;
            }
        }
        
        if (nonzero) {
            std::cout << "? GetSc produced non-zero result!" << std::endl;
        } else {
            std::cout << "??  GetSc returned all zeros" << std::endl;
        }
        
        delete[] sc2;
    } else {
        std::cout << "? GetSc returned null" << std::endl;
    }
    
    // Test Case 3: Simple test cases
    std::cout << "\n--- Test Case 3: Simple Test Cases ---" << std::endl;
    
    const char* test_vins[] = {
        "ABCDEFGHIJKLMNOPQRS",  // Last 5: NOPQR
        "12345678901234567890", // Last 5: 67890
        "AAAAAAAAAAAAAAAAAAAA"  // Last 5: AAAAA
    };
    
    for (int t = 0; t < 3; t++) {
        unsigned char test_vin[20] = {0};
        strncpy((char*)test_vin, test_vins[t], sizeof(test_vin) - 1);
        
        std::cout << "\nTest VIN: " << test_vins[t] << std::endl;
        std::cout << "Last 5 chars: ";
        for (int i = 12; i < 17; i++) {
            std::cout << (char)test_vin[i];
        }
        std::cout << std::endl;
        
        unsigned char* sc_test = GetSc(test_vin);
        if (sc_test) {
            printHex("Generated SC", sc_test, 3);
            delete[] sc_test;
        } else {
            std::cout << "? GetSc returned null" << std::endl;
        }
    }
    
    // Test Case 4: Consistency check
    std::cout << "\n--- Test Case 4: Consistency Check ---" << std::endl;
    
    unsigned char* sc_test1 = GetSc(vin);
    unsigned char* sc_test2 = GetSc(vin);
    
    if (sc_test1 && sc_test2) {
        if (memcmp(sc_test1, sc_test2, 3) == 0) {
            std::cout << "? GetSc is consistent (same input produces same output)" << std::endl;
        } else {
            std::cout << "? GetSc is inconsistent" << std::endl;
        }
        delete[] sc_test1;
        delete[] sc_test2;
    } else {
        std::cout << "? Consistency test failed (null results)" << std::endl;
    }
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "GetSc function test completed." << std::endl;
    std::cout << "This function uses libG1_alg1.so and Trans_InputCode function." << std::endl;
    std::cout << "It generates 3-byte SC codes from VIN positions 12-16." << std::endl;
    
    return 0;
}
