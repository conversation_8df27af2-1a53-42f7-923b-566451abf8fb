// Direct C++ test for new Algorithm.cpp functions
// This bypasses JNI and tests the core algorithm functions directly

#include <iostream>
#include <iomanip>
#include <cstring>
#include <dlfcn.h>

void printHex(const char* label, const unsigned char* data, int len) {
    std::cout << std::setw(12) << std::left << label << ": ";
    for (int i = 0; i < len; i++) {
        std::cout << std::hex << std::setfill('0') << std::setw(2) << (int)data[i];
    }
    std::cout << std::dec << std::endl;
}

// Test the core algorithm functions directly
int main() {
    std::cout << "=== Direct C++ Test for Algorithm.cpp Functions ===" << std::endl;
    
    // Test 1: Test GetSc_New function directly
    std::cout << "\n--- Test 1: GetSc_New Function ---" << std::endl;
    
    // Declare function pointer types
    typedef unsigned char* (*GetSc_New_t)(unsigned char*);
    typedef unsigned char* (*GetIncode_New_t)(unsigned char*, unsigned char*);
    
    // Test VIN: "LFPHC7CC6L1B75928" (Windows tool case)
    unsigned char vin[20] = "LFPHC7CC6L1B75928\0\0";
    unsigned char outcode_str[10] = "0A0E0C01";
    
    std::cout << "Input VIN: " << (char*)vin << std::endl;
    std::cout << "Input OutCode: " << (char*)outcode_str << std::endl;
    
    // Try to load and test the G2 library directly
    std::cout << "\n--- Test 2: Direct G2 Library Test ---" << std::endl;
    
    void* g2_handle = dlopen("./lib/libG2_encryption.so", RTLD_LAZY);
    if (g2_handle) {
        std::cout << "? Successfully loaded libG2_encryption.so" << std::endl;
        
        // Try to get G2 functions
        typedef void (*G2_VinToSc_t)(const unsigned char*, unsigned char*);
        typedef void (*G2_CalcInCode_t)(const unsigned char*, const unsigned char*, unsigned char*);
        
        G2_VinToSc_t G2_VinToSc = (G2_VinToSc_t)dlsym(g2_handle, "G2_VinToSc");
        G2_CalcInCode_t G2_CalcInCode = (G2_CalcInCode_t)dlsym(g2_handle, "G2_CalcInCode");
        
        if (G2_VinToSc && G2_CalcInCode) {
            std::cout << "? Found G2 functions in library" << std::endl;
            
            // Test with VIN last 5 characters: "75928"
            unsigned char vin5[5] = {'7', '5', '9', '2', '8'};
            unsigned char sc[32] = {0};  // Use 32 bytes as per vendor spec
            unsigned char outcode[4] = {0x0A, 0x0E, 0x0C, 0x01};
            unsigned char incode[32] = {0};  // Use 32 bytes as per vendor spec
            
            // Call G2 functions
            G2_VinToSc(vin5, sc);
            G2_CalcInCode(outcode, sc, incode);
            
            printHex("VIN5", vin5, 5);
            printHex("OutCode", outcode, 4);
            printHex("SC", sc, 16);  // Show first 16 bytes
            printHex("InCode", incode, 16);  // Show first 16 bytes
            
            // Check if results match Windows tool
            unsigned char expected_sc[16] = {0xD6, 0x08, 0xCE, 0x5D, 0x5A, 0xC6, 0x3D, 0xF6, 
                                            0x8E, 0xD6, 0xAA, 0x97, 0x66, 0x50, 0xD0, 0x11};
            
            std::cout << "\nComparison with Windows tool:" << std::endl;
            printHex("Expected SC", expected_sc, 16);
            
            if (memcmp(sc, expected_sc, 16) == 0) {
                std::cout << "? PERFECT MATCH with Windows tool!" << std::endl;
            } else {
                std::cout << "??  Results don't match Windows tool" << std::endl;
            }
            
        } else {
            std::cout << "? G2 functions not found in library" << std::endl;
            std::cout << "G2_VinToSc: " << (G2_VinToSc ? "Found" : "Not found") << std::endl;
            std::cout << "G2_CalcInCode: " << (G2_CalcInCode ? "Found" : "Not found") << std::endl;
        }
        
        dlclose(g2_handle);
    } else {
        std::cout << "? Failed to load libG2_encryption.so: " << dlerror() << std::endl;
    }
    
    // Test 3: Test other algorithm libraries
    std::cout << "\n--- Test 3: Other Algorithm Libraries ---" << std::endl;
    
    const char* libs[] = {
        "./lib/libG1_alg1.so",
        "./lib/libG1_alg3.so", 
        "./lib/libC303.so",
        "./lib/libencryptalg1.so",
        "./lib/libencryptalg2.so"
    };
    
    for (int i = 0; i < 5; i++) {
        void* handle = dlopen(libs[i], RTLD_LAZY);
        if (handle) {
            std::cout << "? " << libs[i] << " - Loaded successfully" << std::endl;
            dlclose(handle);
        } else {
            std::cout << "? " << libs[i] << " - Failed to load: " << dlerror() << std::endl;
        }
    }
    
    // Test 4: Manual algorithm test (if libraries fail)
    std::cout << "\n--- Test 4: Manual Algorithm Test ---" << std::endl;
    
    // Simple test to verify basic functionality
    unsigned char test_vin[5] = {'7', '5', '9', '2', '8'};
    unsigned char test_sc[16] = {0};
    
    // Simple algorithm for testing (not real G2)
    for (int i = 0; i < 5; i++) {
        test_sc[i] = test_vin[i] ^ 0xAA;
        test_sc[i + 5] = test_vin[i] + 0x10;
        if (i + 10 < 16) test_sc[i + 10] = test_vin[i] - 0x05;
    }
    test_sc[15] = 0x42;
    
    printHex("Test VIN", test_vin, 5);
    printHex("Test SC", test_sc, 16);
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "This test verifies:" << std::endl;
    std::cout << "1. Library loading capabilities" << std::endl;
    std::cout << "2. Function symbol resolution" << std::endl;
    std::cout << "3. Basic algorithm execution" << std::endl;
    std::cout << "4. Result comparison with expected values" << std::endl;
    
    return 0;
}
