## 要不要在每个导出的函数上写public属性？

```cpp
__attribute__((visibility("default")))
```

取决于**怎样控制符号可见性**。下面把常见做法放在一张表里，方便快速决策。

| 做法                                                       | 需要在函数前写 `visibility("default")` 吗？                  | 适用场景                                 | 优缺点                                                       |
| ---------------------------------------------------------- | ------------------------------------------------------------ | ---------------------------------------- | ------------------------------------------------------------ |
| **不指定任何额外选项**（默认 `-fvisibility=default`）      | **不需要**                                                   | 小型项目、无需隐藏内部符号               | 编译最简单；但是 so 会把 **所有** 非 `static` 函数都导出，符号表臃肿，容易 ABI 泄漏 |
| **全局 `-fvisibility=hidden`**                             | **必须** 为要导出的每个 API 加 `__attribute__((visibility("default")))` 或用 `__declspec(dllexport)` 宏 | 想把内部实现全部隐藏，只暴露少量 API     | 控制最精细；但得在每个导出点写宏，维护工作量稍大             |
| **链接脚本 / 版本脚本**`-Wl,--version-script=exports.map`  | 函数上 **可以不写** attribute，只要脚本里列了它              | 多平台大型库；需要一次性列出所有公共 API | 最清晰；脚本易读、集中管理，但对新手稍难                     |
| **`-Wl,--export-dynamic`**（或 CMake `EXPORT_ALL_SYMBOLS`) | 不需要                                                       | 临时调试，想把全部符号都扔出来           | 简单粗暴；等同关闭隐藏，发布版不推荐                         |

------

## 最简单能用、又不易出错的方案

### ① **保持默认可见性**

直接 **去掉** `-fvisibility=hidden` 之类选项，不写任何 attribute。
 只要在头文件里：

```cpp
#ifdef __cplusplus
extern "C" {
#endif
void G2_VinToSc(const unsigned char*, unsigned char*);
void G2_CalcInCode(const unsigned char*, const unsigned char*, unsigned char*);
#ifdef __cplusplus
}
#endif
```

重新编 so，`nm -D` 里就能看到这两个符号，`dlsym` 能成功。

### ② **想隐藏内部实现再显式导出少量 API**

1. **编译**时加

   ```bash
   -fvisibility=hidden
   ```

2. 给要暴露的函数包宏：

   ```cpp
   #define G2_API  __attribute__((visibility("default")))
   extern "C" {
       G2_API void G2_VinToSc(const unsigned char*, unsigned char*);
       G2_API void G2_CalcInCode(const unsigned char*, const unsigned char*, unsigned char*);
   }
   ```

   以后只要写 `G2_API` 就行，省得每行手敲。

### ③ **用版本脚本（推荐给供应商）**

```
exports.map
G2EncryptionVer {
    global:
        G2_VinToSc;
        G2_CalcInCode;
    local:
        *;
};
```

链接时：

```bash
g++ -shared -Wl,--version-script=exports.map -o libG2Encryption.so obj1.o obj2.o
```

只要脚本里列出名字，就不必在源码里写 attribute，也不必担心大小写或 mangling（必须是 **extern "C"**）。

------

## 小结

- **如果只是为了让 `dlsym` 找到符号，而你并不关心隐藏内部实现**——
   最快的办法是 **去掉 `-fvisibility=hidden`**，什么 attribute 也不用写。
- **如果一定要隐藏**，就二选一：
  - 在函数声明/定义前加 `__attribute__((visibility("default")))`（或宏）；
  - 用 **版本脚本** 把要导出的名字列出来。

无论哪种方式，**记得用 `extern "C"`**，否则 C++ 名字会被 mangle，
 `dlsym("G2_VinToSc")` 依然找不到。