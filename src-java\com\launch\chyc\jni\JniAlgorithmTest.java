package com.launch.chyc.jni;

import com.launch.core.jni.StringWraper;

public class JniAlgorithmTest {

    static {                               System.load(System.getProperty("user.dir") + "/bin/libJniAlgorithm.so");
    }

    //     private native void getScAndIncode(String vin, String out,
                                       StringWraper sc, StringWraper in);

    public static void main(String[] args) {
        String vin  = "LFPHC7CC6L1B75928";
        String out  = "0A0E0C";

        StringWraper sc  = new StringWraper();
        StringWraper inc = new StringWraper();

        new JniAlgorithmTest().getScAndIncode(vin, out, sc, inc);

        System.out.println("SC     = " + sc.value);
        System.out.println("Incode = " + inc.value);
    }
}
