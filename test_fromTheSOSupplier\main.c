#include <stdio.h>
#include <dlfcn.h>
#include "G2_encryption.h"

void hex_print(const char* label, const uint8_t* data, size_t len)
{
    printf("%-8s: ", label);
    for(size_t i=0; i<len; i++) {
        printf("%02X", data[i]);
    }
    printf("\n");
}

int main()
{
    void* handle = dlopen("./libG2_encryption.so", RTLD_LAZY);
    if(!handle) {
        fprintf(stderr, "Error: %s\n", dlerror());
        return 1;
    }

    // 加载函数符号
    void (*G2_VinToSc)(const uint8_t*, uint8_t*) = dlsym(handle, "G2_VinToSc");
    void (*G2_CalcInCode)(const uint8_t*, const uint8_t*, uint8_t*) = dlsym(handle, "G2_CalcInCode");

    // 测试数据
    uint8_t vin[VIN_LEN] = {0x37,0x35,0x39,0x32,0x38,0x00,0x00,0x00,0x01,0x00,
                           0x00,0x00,0x00,0x00,0x00,0x00,0x40};
    uint8_t outcode[OUTCODE_LEN] = {0x0A,0x0E,0x0C,0x01};
    uint8_t sc[SC_LEN] = {0}, incode[INCODE_LEN] = {0};

    // 执行算法
    G2_VinToSc(vin, sc);
    G2_CalcInCode(outcode, sc, incode);

    // 验证输出
    hex_print("VIN", vin, VIN_LEN);
    hex_print("OutCode", outcode, OUTCODE_LEN);
    hex_print("Sc", sc, SC_LEN);
    hex_print("InCode", incode, INCODE_LEN);

    dlclose(handle);
    return 0;
}