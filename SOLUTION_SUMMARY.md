# JNI项目问题解决方案总结

## 问题描述
Java测试用例调用JNI库后，SC和Incode的结果全为0。

## 根本原因分析

### 1. 动态库路径错误 ✅ 已修复
**问题**: 代码中使用相对路径 `"libG2Encryption.so"` 加载G2加密库，但实际库文件位于 `lib/` 目录下。

**修复**: 将所有 `dlopen("libG2Encryption.so", RTLD_LAZY)` 改为 `dlopen("./lib/libG2Encryption.so", RTLD_LAZY)`

**影响的函数**:
- `GetSc()` (行141)
- `GetIncode()` (行173)
- `GetSc_G2()` (行205)
- `GetIncode_G2()` (行282)

### 2. VIN码处理不正确 ✅ 已修复
**问题**: G2算法要求输入VIN的最后5个字符，但原代码传递了完整的17字符VIN。

**修复**: 在 `GetSc_G2()` 函数中提取VIN的最后5个字符：
```cpp
size_t vinLen = strlen(vin17);
if (vinLen >= 5) {
    const char* last5 = vin17 + (vinLen - 5);
    fun(reinterpret_cast<const unsigned char*>(last5), sc16);
}
```

**依据**: G2_encryption.h文档明确说明VIN参数应该是"VIN的最后5个字节（ASCII码值）"

### 3. 缺少错误处理和调试信息 ✅ 已修复
**问题**: 原代码缺少详细的错误检查和日志记录，难以诊断问题。

**修复**: 添加了完整的调试日志系统：
- 库加载状态检查
- 函数符号查找验证
- 输入参数记录
- 算法执行结果记录
- 错误信息详细记录

## 修复后的代码特点

### 增强的错误处理
```cpp
void* h = dlopen("./lib/libG2Encryption.so", RTLD_LAZY);
if (!h) {
    ss << "Failed to load libG2Encryption.so: " << dlerror();
    jniLogMessage(ss.str());
    return sc16;  // 返回全零数组
}
```

### 详细的调试日志
- 输入参数验证和记录
- 库加载状态确认
- 函数查找结果验证
- 算法执行过程跟踪
- 输出结果完整记录

### 正确的VIN处理
- 自动提取VIN最后5个字符
- 参数有效性检查
- 长度验证

## 测试建议

### 1. 重新编译
```bash
make clean && make
```

### 2. 运行Java测试
```bash
java -cp src-java com.launch.chyc.jni.TestMain
```

### 3. 检查调试日志
```bash
cat /tmp/jni_algorithm.log
```

### 4. 预期结果
对于测试数据:
- VIN: "LSJW26N93JS123456" (最后5位: "23456")
- OutCode: "A1B2C3"

应该得到非零的SC16和Incode16结果。

## 可能的其他问题

### 1. 库架构不匹配
如果问题仍然存在，可能是libG2Encryption.so不是为当前架构编译的。
检查方法: `file lib/libG2Encryption.so`

### 2. 库依赖缺失
G2库可能依赖其他系统库。
检查方法: `ldd lib/libG2Encryption.so`

### 3. 权限问题
确保库文件有执行权限。
修复方法: `chmod +x lib/libG2Encryption.so`

## 验证步骤

1. **确认库文件存在**: `ls -la lib/libG2Encryption.so`
2. **检查库架构**: `file lib/libG2Encryption.so`
3. **验证符号导出**: `nm -D lib/libG2Encryption.so | grep G2`
4. **运行测试**: `java -cp src-java com.launch.chyc.jni.TestMain`
5. **查看日志**: `tail -f /tmp/jni_algorithm.log`

## 成功标志

修复成功后，应该看到：
1. Java输出显示非零的SC和Incode值
2. 日志文件显示成功加载库和找到函数
3. 日志中显示正确的VIN处理（最后5个字符）
4. 算法执行完成并返回有效结果

## 联系信息

如果问题仍然存在，请提供：
1. `/tmp/jni_algorithm.log` 的完整内容
2. `file lib/libG2Encryption.so` 的输出
3. Java程序的完整输出
4. 系统架构信息 (`uname -a`)
